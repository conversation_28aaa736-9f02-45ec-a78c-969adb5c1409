package com.hellofresh.oms.orderManagementHttp.imports.validator

import com.hellofresh.oms.model.PackagingType
import com.hellofresh.oms.model.ShipMethodEnum
import com.hellofresh.oms.model.UOM
import com.hellofresh.oms.model.YearWeek
import com.hellofresh.oms.orderManagement.generated.api.model.DeliveryWindowDto
import com.hellofresh.oms.orderManagement.generated.api.model.LineError
import com.hellofresh.oms.orderManagementHttp.configuration.AbstractIntegrationTest
import com.hellofresh.oms.orderManagementHttp.distributionCenters.DistributionCentersRepository
import com.hellofresh.oms.orderManagementHttp.emergencyReason.EmergencyReasonRepository
import com.hellofresh.oms.orderManagementHttp.imports.parser.BatchOrderRowDto
import com.hellofresh.oms.orderManagementHttp.order.getDistributionCenterEntity
import com.hellofresh.oms.orderManagementHttp.order.getEmergencyReasonEntity
import com.hellofresh.oms.orderManagementHttp.order.getSkuEntity
import com.hellofresh.oms.orderManagementHttp.order.getSupplierSimpleEntity
import com.hellofresh.oms.orderManagementHttp.sku.SkuRepository
import com.hellofresh.oms.orderManagementHttp.supplier.SupplierRepository
import com.hellofresh.oms.orderManagementHttp.supplier.out.SupplierSimpleRepository
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.format.DateTimeFormatter
import java.util.UUID
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.empty
import org.hamcrest.Matchers.equalTo
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DynamicTest
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Tag
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase.Replace.NONE
import org.springframework.boot.test.context.SpringBootTest

@Suppress("MaxLineLength")
@Tag("integration")
@SpringBootTest
@AutoConfigureTestDatabase(replace = NONE)
class BatchCreateOrderValidatorIntegrationTest : AbstractIntegrationTest() {
    @Autowired
    private lateinit var subject: BatchCreatePurchaseOrderValidator

    @Autowired
    private lateinit var distributionCentersRepository: DistributionCentersRepository

    @Autowired
    private lateinit var supplierRepository: SupplierRepository

    @Autowired
    private lateinit var supplierSimpleRepository: SupplierSimpleRepository

    @Autowired
    private lateinit var emergencyReasonRepository: EmergencyReasonRepository

    @Autowired
    private lateinit var skuRepository: SkuRepository

    @AfterEach
    fun tearDown() {
        distributionCentersRepository.deleteAll()
        supplierRepository.deleteAll()
        emergencyReasonRepository.deleteAll()
        skuRepository.deleteAll()
    }

    @BeforeEach
    fun saveDefaultEntities() {
        createSingleParsedLines().forEach {
            saveDc(it.distributionCenterValue, VALID_MARKET)
            saveSupplier(it.supplierCodeValue.toInt(), VALID_MARKET)
            saveReason(it.reason, VALID_MARKET)
            saveSku(it.skuValue, VALID_MARKET)
        }
    }

    @Test
    fun `should respond successfully with validated list of DTOs`() {
        val parsedLines = createSingleParsedLines(
            distributionCenterValue = "ZZ",
            supplierCodeValue = "7645984",
            reason = "Just for fun",
            skuValue = "ABC-123-1",
            comments = "This is a comment",
            orderSize = "1000",
            bufferValue = "25",
        )

        val givenLine = parsedLines.first()
        val givenDc = saveDc(givenLine.distributionCenterValue, VALID_MARKET)
        val givenSupplier = saveSupplier(givenLine.supplierCodeValue.toInt(), VALID_MARKET)
        val givenReason = saveReason(givenLine.reason, VALID_MARKET)
        val givenSku = saveSku(givenLine.skuValue, VALID_MARKET)

        val expectedOrderSizeWithBuffer = 1250.toBigDecimal()
        val expectedValidatedDto = BatchOrderRowValidatedDto(
            rowNumber = 2,
            yearWeek = YearWeek(givenLine.weekValue),
            distributionCenter = givenDc,
            supplier = givenSupplier,
            emergencyReason = givenReason,
            sku = givenSku,
            bufferValue = BigDecimal(givenLine.bufferValue),
            caseSize = BigDecimal(givenLine.caseSize),
            orderSize = expectedOrderSizeWithBuffer,
            price = BigDecimal(givenLine.price),
            packagingType = PackagingType.valueOf(givenLine.orderUnitValue),
            uom = BatchRowUom.entries.first { it.value == givenLine.caseUom }.toUOM(),
            shipMethod = BatchRowShipMethod.entries.first { it.value == givenLine.shipMethod }.toShipMethodEnum(),
            deliveryWindow = DeliveryWindowDto(
                start = LocalDateTime.of(
                    givenLine.deliveryDate.toLocalDate(),
                    givenLine.startTime.toLocalTime(),
                ),
                end = LocalDateTime.of(
                    givenLine.deliveryDate.toLocalDate(),
                    givenLine.endTime.toLocalTime(),
                ),
            ),
            comments = givenLine.comments,
        )

        val result = subject.validate(parsedLines, listOf(VALID_MARKET))

        assertThat(result.validLines, equalTo(listOf(expectedValidatedDto)))
    }

    @Nested
    inner class DateTimeValidations {
        @Test
        fun `should have error when week_value is not valid`() {
            // given
            val incorrectWeek = "2024-37"

            // when
            val result = subject.validate(
                createSingleParsedLines(weekValue = incorrectWeek),
                listOf(VALID_MARKET),
            ).lineErrors

            // then
            assertThat(
                result,
                equalTo(
                    setOf(LineError(2, """value for "week.value" must be in the correct format "YYYY-'W'ww"""")),
                ),
            )
        }

        @Test
        fun `should have error when deliveryDate is not a valid date`() {
            // given
            val incorrectDeliveryDate = "09-06-2024"

            // when
            val result = subject.validate(
                createSingleParsedLines(deliveryDate = incorrectDeliveryDate),
                listOf(VALID_MARKET),
            ).lineErrors

            // then
            assertThat(
                result,
                equalTo(
                    setOf(LineError(2, """value for "deliveryDate" must be in the correct format "yyyy-MM-dd"""")),
                ),
            )
        }

        @Test
        fun `should have error when deliveryDate is in the past`() {
            // given
            val incorrectDeliveryDate = "2024-01-01"

            // when
            val result = subject.validate(
                createSingleParsedLines(
                    deliveryDate = incorrectDeliveryDate,
                ),
                listOf(VALID_MARKET),
            ).lineErrors

            // then
            assertThat(
                result,
                equalTo(
                    setOf(LineError(2, """value for "deliveryDate" must be today's date or a future date""")),
                ),
            )
        }

        @Test
        fun `should have error when startTime is not a valid time`() {
            // given
            val incorrectStartTime = "2024-12-01T06:00"

            // when
            val result = subject.validate(
                createSingleParsedLines(startTime = incorrectStartTime),
                listOf(VALID_MARKET),
            ).lineErrors

            // then
            assertThat(
                result,
                equalTo(
                    setOf(LineError(2, """value for "startTime" must be in the correct format "H:m"""")),
                ),
            )
        }

        @Test
        fun `should have error when endTime is not a valid time`() {
            // given
            val incorrectEndTime = "2024-12-01T08:00"

            // when
            val result = subject.validate(
                createSingleParsedLines(endTime = incorrectEndTime),
                listOf(VALID_MARKET),
            ).lineErrors

            // then
            assertThat(
                result,
                equalTo(
                    setOf(LineError(2, """value for "endTime" must be in the correct format "H:m"""")),
                ),
            )
        }
    }

    @Nested
    inner class NumberValidations {
        @Test
        fun `should have error when orderSize is not a valid number`() {
            // given
            val incorrectOrderSize = "not a number"

            // when
            val result = subject.validate(
                createSingleParsedLines(orderSize = incorrectOrderSize),
                listOf(VALID_MARKET),
            ).lineErrors

            // then
            assertThat(
                result,
                equalTo(
                    setOf(LineError(2, """value for "orderSize" must be a positive number""")),
                ),
            )
        }

        @Test
        fun `should have error when orderSize is not a positive number`() {
            // given
            val incorrectOrderSize = "-1"

            // when
            val result = subject.validate(
                createSingleParsedLines(orderSize = incorrectOrderSize),
                listOf(VALID_MARKET),
            ).lineErrors

            // then
            assertThat(
                result,
                equalTo(
                    setOf(LineError(2, """value for "orderSize" must be a positive number""")),
                ),
            )
        }

        @Test
        fun `should have error when orderSize is zero`() {
            // given
            val incorrectOrderSize = "0"

            // when
            val result = subject.validate(
                createSingleParsedLines(orderSize = incorrectOrderSize),
                listOf(VALID_MARKET),
            ).lineErrors

            // then
            assertThat(
                result,
                equalTo(
                    setOf(LineError(2, """value for "orderSize" must be a positive number""")),
                ),
            )
        }

        @Test
        fun `should have error when buffer_value is not a valid number`() {
            // given
            val incorrectBufferValue = "not a number"

            // when
            val result = subject.validate(
                createSingleParsedLines(bufferValue = incorrectBufferValue),
                listOf(VALID_MARKET),
            ).lineErrors

            // then
            assertThat(
                result,
                equalTo(
                    setOf(LineError(2, """value for "buffer.value" must be zero or a positive number""")),
                ),
            )
        }

        @Test
        fun `should have error when buffer_value is not zero nor a positive number`() {
            // given
            val incorrectBufferValue = "-20"

            // when
            val result = subject.validate(
                createSingleParsedLines(bufferValue = incorrectBufferValue),
                listOf(VALID_MARKET),
            ).lineErrors

            // then
            assertThat(
                result,
                equalTo(
                    setOf(LineError(2, """value for "buffer.value" must be zero or a positive number""")),
                ),
            )
        }

        @Test
        fun `should have error when caseSize is not a valid number`() {
            // given
            val incorrectCaseSize = "not a number"

            // when
            val result = subject.validate(
                createSingleParsedLines(caseSize = incorrectCaseSize, orderUnitValue = "CASE_TYPE"),
                listOf(VALID_MARKET),
            ).lineErrors

            // then
            assertThat(
                result,
                equalTo(
                    setOf(LineError(2, """value for "case.size" must be a positive number""")),
                ),
            )
        }

        @Test
        fun `should have error when caseSize is not a positive number`() {
            // given
            val incorrectCaseSize = "-100"

            // when
            val result = subject.validate(
                createSingleParsedLines(caseSize = incorrectCaseSize, orderUnitValue = "CASE_TYPE"),
                listOf(VALID_MARKET),
            ).lineErrors

            // then
            assertThat(
                result,
                equalTo(
                    setOf(LineError(2, """value for "case.size" must be a positive number""")),
                ),
            )
        }

        @Test
        fun `should have error when price is not a valid number`() {
            // given
            val incorrectPrice = "not a number"

            // when
            val result = subject.validate(
                createSingleParsedLines(price = incorrectPrice),
                listOf(VALID_MARKET),
            ).lineErrors

            // then
            assertThat(
                result,
                equalTo(
                    setOf(LineError(2, """value for "price" must be zero or a positive number""")),
                ),
            )
        }

        @Test
        fun `should have error when price is not a positive number`() {
            // given
            val incorrectPrice = "-15"

            // when
            val result = subject.validate(
                createSingleParsedLines(price = incorrectPrice),
                listOf(VALID_MARKET),
            ).lineErrors

            // then
            assertThat(
                result,
                equalTo(
                    setOf(LineError(2, """value for "price" must be zero or a positive number""")),
                ),
            )
        }
    }

    @Nested
    inner class EnumValidations {
        @Test
        fun `should parse UNIT_TYPE correctly`() {
            // when
            val result = subject.validate(createSingleParsedLines(orderUnitValue = "UNIT_TYPE"), listOf(VALID_MARKET))

            // then
            assertThat(result.lineErrors, empty())
            assertThat(result.validLines.map { it.packagingType }, equalTo(listOf(PackagingType.UNIT_TYPE)))
        }

        @Test
        fun `should parse CASE_TYPE correctly`() {
            // when
            val result = subject.validate(
                createSingleParsedLines(orderUnitValue = "CASE_TYPE", caseSize = "10"),
                listOf(VALID_MARKET),
            )

            // then
            assertThat(result.lineErrors, empty())
            assertThat(result.validLines.map { it.packagingType }, equalTo(listOf(PackagingType.CASE_TYPE)))
        }

        @TestFactory
        fun `should parse caseUom correctly to expected type`() = listOf(
            "oz" to UOM.OZ,
            "kg" to UOM.KG,
            "lbs" to UOM.LBS,
            "L" to UOM.L,
            "gal" to UOM.GAL,
            "unit" to UOM.UNIT,
        ).map { (givenValue, expectedType) ->
            DynamicTest.dynamicTest("should parse $givenValue to $expectedType") {
                // when
                val result = subject.validate(
                    createSingleParsedLines(caseUom = givenValue),
                    listOf(VALID_MARKET),
                ).validLines

                // then
                assertThat(result.first().uom, equalTo(expectedType))
            }
        }

        @TestFactory
        fun `should parse shipMethod correctly to expected type`() = listOf(
            "Others" to ShipMethodEnum.OTHER,
            "Freight On Board" to ShipMethodEnum.FREIGHT_ON_BOARD,
            "Crossdock" to ShipMethodEnum.CROSSDOCK,
            "Vendor Delivered" to ShipMethodEnum.VENDOR,
        ).map { (givenValue, expectedType) ->
            DynamicTest.dynamicTest("should parse $givenValue to $expectedType") {
                // when
                val result = subject.validate(
                    createSingleParsedLines(shipMethod = givenValue),
                    listOf(VALID_MARKET),
                ).validLines

                // then
                assertThat(result.first().shipMethod, equalTo(expectedType))
            }
        }

        @Test
        fun `should have error when orderUnit_value is not a valid PackagingType`() {
            // given
            val incorrectOrderUnitValue = "INVALID_TYPE"

            // when
            val result = subject.validate(
                createSingleParsedLines(orderUnitValue = incorrectOrderUnitValue),
                listOf(VALID_MARKET),
            ).lineErrors

            // then
            assertThat(
                result,
                equalTo(
                    setOf(
                        LineError(
                            2,
                            """value for "orderUnit.value" must be one of [UNIT_TYPE, CASE_TYPE]""",
                        ),
                    ),
                ),
            )
        }

        @Test
        fun `should have error when caseUom is not a valid UOM`() {
            // given
            val incorrectCaseUom = "INVALID_UOM"

            // when
            val result = subject.validate(
                createSingleParsedLines(caseUom = incorrectCaseUom),
                listOf(VALID_MARKET),
            ).lineErrors

            // then
            assertThat(
                result,
                equalTo(
                    setOf(
                        LineError(
                            2,
                            """value for "case.uom" must be one of [oz, kg, lbs, L, gal, unit(s)]"""
                        )
                    ),
                ),
            )
        }

        @Test
        fun `should have error when shipMethod is not a valid ShipMethodEnum`() {
            // given
            val incorrectShipMethod = "INVALID_SHIP_METHOD"

            // when
            val result = subject.validate(
                createSingleParsedLines(shipMethod = incorrectShipMethod),
                listOf(VALID_MARKET),
            ).lineErrors

            // then
            assertThat(
                result,
                equalTo(
                    setOf(
                        LineError(
                            2,
                            """value for "shipMethod" must be one of [Other, Freight On Board, Crossdock, Vendor Delivered]""",
                        ),
                    ),
                ),
            )
        }
    }

    @Nested
    inner class DatabaseValidations {
        @Test
        fun `should have errors when entities do not exist in the database`() {
            val invalidDc = "ZZ"
            val invalidSupplier = 548652
            val invalidReason = "Invalid Reason"
            val invalidSku = "ABC-00-000000-1"

            val result = subject.validate(
                createSingleParsedLines(
                    distributionCenterValue = invalidDc,
                    supplierCodeValue = invalidSupplier.toString(),
                    reason = invalidReason,
                    skuValue = invalidSku,
                ),
                listOf(VALID_MARKET),
            ).lineErrors

            assertThat(
                result,
                equalTo(
                    setOf(
                        LineError(2, """distribution center "$invalidDc" does not exist"""),
                        LineError(2, """supplier "$invalidSupplier" does not exist"""),
                        LineError(2, """emergency reason "$invalidReason" does not exist"""),
                        LineError(2, """sku "$invalidSku" does not exist"""),
                    ),
                ),
            )
        }

        @Test
        fun `should have no errors when all entities are linked to the same market`() {
            val givenMarket = VALID_MARKET

            val givenDcCode = "AB"
            saveDc(givenDcCode, givenMarket)

            val supplierCode = 113000
            saveSupplier(supplierCode, givenMarket)

            val givenReason = "Multi-Week"
            saveReason(givenReason, givenMarket)

            val givenSkuCode = "PCK-00-005297-1"
            saveSku(givenSkuCode, givenMarket)

            val result = subject.validate(
                createSingleParsedLines(
                    distributionCenterValue = givenDcCode,
                    supplierCodeValue = supplierCode.toString(),
                    reason = givenReason,
                    skuValue = givenSkuCode,
                ),
                listOf(VALID_MARKET),
            ).lineErrors

            assertThat(result, empty())
        }

        @Test
        fun `should have error when supplier, reason and sku market is different from dc market`() {
            val givenDcCode = "OI"
            val givenDcMarket = "it"
            saveDc(givenDcCode, givenDcMarket)

            val givenSupplierCode = 254121
            saveSupplier(givenSupplierCode, "dach")

            val givenReason = "Wrong Order"
            saveReason(givenReason, "dkse")

            val givenSkuCode = "HGT-54-00045-1"
            saveSku(givenSkuCode, "ca")

            val result = subject.validate(
                createSingleParsedLines(
                    distributionCenterValue = givenDcCode,
                    supplierCodeValue = givenSupplierCode.toString(),
                    reason = givenReason,
                    skuValue = givenSkuCode,
                ),
                listOf(givenDcMarket),
            ).lineErrors

            assertThat(
                result,
                equalTo(
                    setOf(
                        LineError(
                            2,
                            """supplier "$givenSupplierCode" does not exist in market "$givenDcMarket".
                            |Market is defined by the given distribution center
                            """.trimMargin(),
                        ),
                        LineError(
                            2,
                            """emergency reason "$givenReason" does not exist in market "$givenDcMarket".
                            |Market is defined by the given distribution center
                            """.trimMargin(),
                        ),
                        LineError(
                            2,
                            """sku "$givenSkuCode" does not exist in market "$givenDcMarket".
                            |Market is defined by the given distribution center
                            """.trimMargin(),
                        ),
                    ),
                ),
            )
        }
    }

    @Nested
    inner class OrderUnitValidations {
        @Test
        fun `should have error when orderUnit_value is PALLET_TYPE`() {
            // when
            val result = subject.validate(
                createSingleParsedLines(orderUnitValue = "PALLET_TYPE"),
                listOf(VALID_MARKET),
            ).lineErrors

            // then
            assertThat(
                result,
                equalTo(
                    setOf(
                        LineError(
                            2,
                            """value for "orderUnit.value" must be one of [UNIT_TYPE, CASE_TYPE]""",
                        ),
                    ),
                ),
            )
        }

        @Test
        fun `should have error when orderUnit_value is CASE_TYPE and caseSize is zero`() {
            // when
            val result = subject.validate(
                createSingleParsedLines(orderUnitValue = "CASE_TYPE", caseSize = "0"),
                listOf(VALID_MARKET),
            ).lineErrors

            // then
            assertThat(
                result,
                equalTo(
                    setOf(
                        LineError(2, """value for "case.size" must be a positive number"""),
                    ),
                ),
            )
        }
    }

    @Nested
    inner class AllowedMarketsValidations {
        @Test
        fun `should have error when dc is not in the allowedMarkets list`() {
            // when
            val result = subject.validate(
                createSingleParsedLines(),
                listOf("purchasing.invalid.all.manager"),
            ).lineErrors

            // then
            assertThat(
                result,
                equalTo(
                    setOf(
                        LineError(
                            2,
                            """user has no permission on the distribution center market. DistributionCenter = "IT", Market = "us"""",
                        ),
                    ),
                ),
            )
        }

        @Test
        fun `should have error when allowedMarkets is empty`() {
            // when
            val result = subject.validate(createSingleParsedLines(), emptyList()).lineErrors

            // then
            assertThat(
                result,
                equalTo(
                    setOf(
                        LineError(
                            2,
                            """user has no permission on the distribution center market. DistributionCenter = "IT", Market = "us"""",
                        ),
                    ),
                ),
            )
        }

        @Test
        fun `should have error when file has multiple markets and user is not allowed on some`() {
            // given
            val givenMarkets = listOf("us", "it")
            val givenParsedLinesMarketPair = listOf(
                "us" to createBatchPoRow(distributionCenterValue = "AB", reason = "Multi-Week-us", supplierCodeValue = "113000"),
                "it" to createBatchPoRow(distributionCenterValue = "CD", reason = "Multi-Week-it", supplierCodeValue = "254121"),
                "dach" to createBatchPoRow(distributionCenterValue = "EF", reason = "Multi-Week-dach", supplierCodeValue = "6253014"),
            )

            givenParsedLinesMarketPair.forEach { (market, row) ->
                saveDc(row.distributionCenterValue, market)
                saveSupplier(row.supplierCodeValue.toInt(), market)
                saveReason(row.reason, market)
                saveSku(row.skuValue, market)
            }

            // when
            val result = subject.validate(givenParsedLinesMarketPair.map { it.second }, givenMarkets).lineErrors

            // then
            assertThat(
                result,
                equalTo(
                    setOf(
                        LineError(
                            2,
                            """user has no permission on the distribution center market. DistributionCenter = "EF", Market = "dach"""",
                        ),
                    ),
                ),
            )
        }
    }

    @Nested
    inner class CommentsValidations {
        @Test
        fun `should trim comments to 240 characters`() {
            val givenCommentsWith400Characters =
                """Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Aenean commodo ligula eget dolor. Aenean massa. Cum sociis natoque penatibus et magnis dis parturient montes, nascetur ridiculus mus. Donec quam felis, ultricies nec, pellentesque eu, pretium quis, sem. Nulla consequat massa quis enim. Donec pede justo, fringilla vel, aliquet nec, vulputate eget, arcu. In enim justo, rhoncus ut, imperdiet a"""
            val expectedCommentsWith240Characters =
                """Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Aenean commodo ligula eget dolor. Aenean massa. Cum sociis natoque penatibus et magnis dis parturient montes, nascetur ridiculus mus. Donec quam felis, ultricies nec, pellentesque eu"""

            val parsedLines = createSingleParsedLines(
                distributionCenterValue = "ZZ",
                supplierCodeValue = "7645984",
                reason = "Just for fun",
                skuValue = "ABC-123-1",
                comments = givenCommentsWith400Characters,
                orderSize = "1000",
                bufferValue = "0",
            )

            val givenLine = parsedLines.first()
            val givenDc = saveDc(givenLine.distributionCenterValue, VALID_MARKET)
            val givenSupplier = saveSupplier(givenLine.supplierCodeValue.toInt(), VALID_MARKET)
            val givenReason = saveReason(givenLine.reason, VALID_MARKET)
            val givenSku = saveSku(givenLine.skuValue, VALID_MARKET)

            val expectedValidatedDto = BatchOrderRowValidatedDto(
                rowNumber = 2,
                yearWeek = YearWeek(givenLine.weekValue),
                distributionCenter = givenDc,
                supplier = givenSupplier,
                emergencyReason = givenReason,
                sku = givenSku,
                bufferValue = BigDecimal(givenLine.bufferValue),
                caseSize = BigDecimal(givenLine.caseSize),
                orderSize = BigDecimal(givenLine.orderSize),
                price = BigDecimal(givenLine.price),
                packagingType = PackagingType.valueOf(givenLine.orderUnitValue),
                uom = BatchRowUom.entries.first { it.value == givenLine.caseUom }.toUOM(),
                shipMethod = BatchRowShipMethod.entries.first { it.value == givenLine.shipMethod }.toShipMethodEnum(),
                deliveryWindow = DeliveryWindowDto(
                    start = LocalDateTime.of(
                        givenLine.deliveryDate.toLocalDate(),
                        givenLine.startTime.toLocalTime(),
                    ),
                    end = LocalDateTime.of(
                        givenLine.deliveryDate.toLocalDate(),
                        givenLine.endTime.toLocalTime(),
                    ),
                ),
                comments = expectedCommentsWith240Characters,
            )

            val result = subject.validate(parsedLines, listOf(VALID_MARKET))

            assertThat(result.validLines, equalTo(listOf(expectedValidatedDto)))
        }
    }

    @Suppress("LongParameterList")
    private fun createSingleParsedLines(
        weekValue: String = "2024-W37",
        distributionCenterValue: String = "IT",
        customOrderNumberCode: String = "",
        reason: String = "Multi-Week",
        supplierCodeValue: String = "113000",
        deliveryDate: String = LocalDate.now().toString(),
        startTime: String = LocalTime.now().plusHours(1).format(DateTimeFormatter.ofPattern("HH:mm")).toString(),
        endTime: String = LocalTime.now().plusHours(4).format(DateTimeFormatter.ofPattern("HH:mm")).toString(),
        skuValue: String = "PCK-00-005297-1",
        orderSize: String = "1200",
        orderUnitValue: String = "UNIT_TYPE",
        bufferValue: String = "2.5",
        caseSize: String = "0",
        caseUom: String = "unit",
        price: String = "1.80",
        shipMethod: String = "Vendor Delivered",
        comments: String = "",
    ) = listOf(
        createBatchPoRow(
            weekValue,
            distributionCenterValue,
            customOrderNumberCode,
            reason,
            supplierCodeValue,
            deliveryDate,
            startTime,
            endTime,
            skuValue,
            orderSize,
            orderUnitValue,
            bufferValue,
            caseSize,
            caseUom,
            price,
            shipMethod,
            comments,
        ),
    )

    @Suppress("LongParameterList")
    private fun createBatchPoRow(
        weekValue: String = "2024-W37",
        distributionCenterValue: String = "IT",
        customOrderNumberCode: String = "",
        reason: String = "Multi-Week",
        supplierCodeValue: String = "113000",
        deliveryDate: String = LocalDate.now().toString(),
        startTime: String = LocalTime.now().plusHours(1).format(DateTimeFormatter.ofPattern("H:m")).toString(),
        endTime: String = LocalTime.now().plusHours(4).format(DateTimeFormatter.ofPattern("H:m")).toString(),
        skuValue: String = "PCK-00-005297-1",
        orderSize: String = "1200",
        orderUnitValue: String = "UNIT_TYPE",
        bufferValue: String = "2.5",
        caseSize: String = "0",
        caseUom: String = "unit",
        price: String = "1.80",
        shipMethod: String = "Vendor Delivered",
        comments: String = "",
    ) = BatchOrderRowDto(
        rowNumber = 2,
        weekValue = weekValue,
        distributionCenterValue = distributionCenterValue,
        customOrderNumberCode = customOrderNumberCode,
        reason = reason,
        supplierCodeValue = supplierCodeValue,
        deliveryDate = deliveryDate,
        startTime = startTime,
        endTime = endTime,
        skuValue = skuValue,
        orderSize = orderSize,
        orderUnitValue = orderUnitValue,
        bufferValue = bufferValue,
        caseSize = caseSize,
        caseUom = caseUom,
        price = price,
        shipMethod = shipMethod,
        comments = comments,
    )

    private fun saveDc(dcCode: String, market: String) = distributionCentersRepository.save(
        getDistributionCenterEntity(code = dcCode, market = market),
    )

    private fun saveSupplier(supplierCode: Int, market: String) =
        supplierSimpleRepository.save(getSupplierSimpleEntity(code = supplierCode, market = market))

    private fun saveReason(reason: String, market: String) = emergencyReasonRepository.save(
        getEmergencyReasonEntity(name = reason, market = market, uuid = UUID.randomUUID()),
    )

    private fun saveSku(skuCode: String, market: String) = skuRepository.save(
        getSkuEntity(code = skuCode, market = market),
    )

    companion object {
        private const val VALID_MARKET = "us"
    }
}

private fun String.toLocalDate() = LocalDate.parse(this, DateTimeFormatter.ISO_LOCAL_DATE)

private fun String.toLocalTime() = LocalTime.parse(this, DateTimeFormatter.ISO_LOCAL_TIME)
