package com.hellofresh.oms.orderManagementHttp.supplierSku

import com.hellofresh.oms.model.Permyriad
import com.hellofresh.oms.model.SupplierSkuStatus
import com.hellofresh.oms.orderManagement.generated.api.model.SortApiResponse
import com.hellofresh.oms.orderManagement.generated.api.model.SupplierSkuApiResponse.Uom
import com.hellofresh.oms.orderManagementHttp.supplierSku.intake.toSupplierSkuApiResponse
import com.hellofresh.oms.orderManagementHttp.supplierSku.out.SkuWithPrice
import com.hellofresh.oms.orderManagementHttp.supplierSku.service.domain.SupplierSkuDto
import java.math.BigDecimal
import java.util.UUID
import java.util.stream.Stream
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertAll
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.mockito.kotlin.any
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever
import org.springframework.data.domain.Sort

class SkuWithPriceMapperTest {

    @Test
    fun `should map sku-with-price entity to response dto`() {
        // given
        val skuWithPrice = getSkuWithPriceEntity()
        val sort: Sort = mock()
        val stream: Stream<Sort.Order> = mock()
        val sortApiResponseStream = mock<Stream<SortApiResponse>>()
        val sortList = mock<List<SortApiResponse>>()
        whenever(sort.get()).thenReturn(stream)
        whenever(stream.map<SortApiResponse>(any())).thenReturn(sortApiResponseStream)
        whenever(sortApiResponseStream.toList()).thenReturn(sortList)

        // when
        val result = SupplierSkuDto(
            pageNumber = 1,
            pageSize = 2,
            totalElements = 3,
            totalPages = 4,
            sort = sort,
            supplierSkus = listOf(skuWithPrice)
        ).toSupplierSkuApiResponse()

        // then
        val mappedSku = result.items.first()
        val pageResult = result.pageResult
        assertAll(
            { assertNotNull(mappedSku.id) },
            { assertNotNull(mappedSku.supplierUuid) },
            { assertEquals(Uom.valueOf("L"), mappedSku.uom) },
            { assertEquals("PTN-20202-11", mappedSku.code) },
            { assertEquals("Test cucumber", mappedSku.name) },
            { assertEquals("PTN", mappedSku.skuCategory) },
            { assertEquals(SupplierSkuStatus.ACTIVE.name, mappedSku.status) },
            { assertEquals("1.900", mappedSku.price!!.amount) },
            { assertEquals(10, mappedSku.caseSize) },
            { assertEquals("EUR", mappedSku.price!!.currency) },
            { assertEquals("case_price", mappedSku.price!!.type) },
            { assertEquals("10.50", mappedSku.buffer!!.toString()) },
            { assertEquals(1, pageResult.number) },
            { assertEquals(2, pageResult.pageSize) },
            { assertEquals(BigDecimal(3), pageResult.totalElements) },
            { assertEquals(4, pageResult.totalPages) },
            { assertEquals(sortList, pageResult.sort) },
            { assertEquals(12, mappedSku.casesPerPallet) }
        )
    }

    @Test
    fun `should map UoM to null when value is null`() {
        // given
        val skuWithPrice = getSkuWithPriceEntity().copy(uom = null)
        val sort: Sort = mock()
        val stream: Stream<Sort.Order> = mock()
        val sortApiResponseStream = mock<Stream<SortApiResponse>>()
        val sortList = mock<List<SortApiResponse>>()
        whenever(sort.get()).thenReturn(stream)
        whenever(stream.map<SortApiResponse>(any())).thenReturn(sortApiResponseStream)
        whenever(sortApiResponseStream.toList()).thenReturn(sortList)

        // when
        val result = SupplierSkuDto(
            pageNumber = 1,
            pageSize = 2,
            totalElements = 3,
            totalPages = 4,
            sort = sort,
            supplierSkus = listOf(skuWithPrice)
        ).toSupplierSkuApiResponse()

        // then
        val mappedSku = result.items.first()
        assertNull(mappedSku.uom)
    }

    @ParameterizedTest
    @CsvSource(
        "360000,36.000",
        "12345678,1234.568",
        "1000005,100.000",
        "1000001,100.000",
        "1000006,100.001",
        "1000009,100.001",
    )
    fun `should even round price with 3 meaningful digits`(
        input: Int,
        expectedResult: String
    ) {
        // when

        val result = SupplierSkuDto(
            pageNumber = 1,
            pageSize = 1,
            totalElements = 1,
            totalPages = 1,
            sort = mock(),
            supplierSkus = listOf(getSkuWithPriceEntity(Permyriad(input)))
        ).toSupplierSkuApiResponse()

        // then
        assertEquals(expectedResult, result.items.first().price!!.amount)
    }

    companion object {
        fun getSkuWithPriceEntity(pricePermyriad: Permyriad = Permyriad(19000)) = SkuWithPrice(
            id = UUID.randomUUID(),
            supplierId = UUID.randomUUID(),
            code = "PTN-20202-11",
            status = SupplierSkuStatus.ACTIVE,
            skuCategory = "PTN",
            name = "Test cucumber",
            uom = "l",
            caseSize = 10,
            pricePermyriad = pricePermyriad,
            currency = "EUR",
            priceType = "case_price",
            buffer = Permyriad(1050),
            casesPerPallet = 12
        )
    }
}
