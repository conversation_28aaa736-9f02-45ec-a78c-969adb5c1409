package com.hellofresh.oms.orderManagementHttp.orderOverview.service

import com.hellofresh.oms.model.YearWeek
import com.hellofresh.oms.orderManagement.generated.api.model.TransferOrdersOverviewResponseInner
import com.hellofresh.oms.orderManagementHttp.orderOverview.out.transferOrder.TransferOrderStatusDetails
import com.hellofresh.oms.orderManagementHttp.orderOverview.service.transferOrder.TransferOrderOverviewModel
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Test
import java.math.BigDecimal
import java.util.UUID

class TransferOrderOverviewMapperTest {

    @Test
    fun `should map TransferOrderOverviewModel to API response correctly`() {
        val transferOrderId = UUID.randomUUID()
        val overviewModel = TransferOrderOverviewModel(
            transferOrderId = transferOrderId,
            transferOrderNumber = "TO-2024-001",
            skuCode = "SKU-001",
            status = "STATE_OPEN",
            destinationDcCode = "DC001",
            sourceDcCode = "DC002",
            skuName = "Test Product",
            category = "Fresh",
            skuUom = "kg",
            week = YearWeek("2024-W01"),
            quantityReceived = BigDecimal("10.0"),
            casesReceived = 5,
            caseSize = BigDecimal("2.0"),
            packagingType = "case",
            priceAmount = "EUR 25.50",
            totalQuantity = BigDecimal("10.0"),
            reasonText = "Stock transfer",
            shippingMethod = "Truck",
            assignedBuyerFirstName = "John",
            assignedBuyerLastName = "Doe"
        )

        val result = overviewModel.mapToTransferOrderOverviewModelToApiResponse()

        assertEquals(transferOrderId, result.transferOrderId)
        assertEquals("TO-2024-001", result.transferOrderNumber)
        assertEquals("SKU-001", result.skuCode)
        assertEquals(TransferOrdersOverviewResponseInner.Status.STATE_OPEN, result.status)
        assertEquals("DC001", result.destinationDcCode)
        assertEquals("DC002", result.sourceDcCode)
        assertEquals(TransferOrdersOverviewResponseInner.PackagingType.CASE, result.packagingType)
    }

    @Test
    fun `should map TransferOrderStatusDetails to TransferOrderOverviewModel correctly`() {
        val transferOrderId = UUID.randomUUID()
        val statusDetails = TransferOrderStatusDetails(
            transferOrderId = transferOrderId,
            transferOrderNumber = "TO-2024-003",
            skuCode = "SKU-003",
            status = "STATE_IN_TRANSIT",
            destinationDcCode = "DC005",
            sourceDcCode = "DC006",
            skuName = "Transit Product",
            category = "Dairy",
            skuUom = "liter",
            week = YearWeek("2024-W03"),
            quantityReceived = BigDecimal("30.0"),
            casesReceived = 15,
            caseSize = BigDecimal("2.0"),
            packagingType = "case",
            priceAmount = "EUR 75.00",
            totalQuantity = BigDecimal("30.0"),
            reasonText = "Regular transfer",
            shippingMethod = "Standard",
            assignedBuyerFirstName = "Bob",
            assignedBuyerLastName = "Johnson"
        )

        val result = statusDetails.mapToTransferOrderOverviewModel()

        assertEquals(transferOrderId, result.transferOrderId)
        assertEquals("TO-2024-003", result.transferOrderNumber)
        assertEquals("SKU-003", result.skuCode)
        assertEquals("STATE_IN_TRANSIT", result.status)
        assertEquals("case", result.packagingType)
    }

    @Test
    fun `should handle null values correctly`() {
        val transferOrderId = UUID.randomUUID()
        val statusDetails = TransferOrderStatusDetails(
            transferOrderId = transferOrderId,
            transferOrderNumber = "TO-2024-004",
            skuCode = "SKU-004",
            status = "STATE_OPEN",
            destinationDcCode = "DC007",
            sourceDcCode = "DC008",
            skuName = "Minimal Product",
            category = "Other",
            skuUom = null,
            week = YearWeek("2024-W04"),
            quantityReceived = null,
            casesReceived = null,
            caseSize = null,
            packagingType = "unit",
            priceAmount = "EUR 0.00",
            totalQuantity = BigDecimal("0.0"),
            reasonText = null,
            shippingMethod = null,
            assignedBuyerFirstName = null,
            assignedBuyerLastName = null
        )

        val result = statusDetails.mapToTransferOrderOverviewModel()

        assertEquals(transferOrderId, result.transferOrderId)
        assertNull(result.skuUom)
        assertNull(result.quantityReceived)
        assertNull(result.casesReceived)
        assertNull(result.reasonText)
    }
}
