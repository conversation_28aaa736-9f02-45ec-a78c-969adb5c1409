package com.hellofresh.oms.orderManagementHttp.orderOverview.intake

import com.hellofresh.oms.model.YearWeek
import com.hellofresh.oms.orderManagementHttp.orderOverview.service.TransferOrderOverviewService
import com.hellofresh.oms.orderManagementHttp.orderOverview.service.transferOrder.TransferOrderOverviewModel
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status
import java.math.BigDecimal
import java.util.UUID

@WebMvcTest(TransferOrderOverviewController::class)
class TransferOrderOverviewControllerTest {

    @Autowired
    private lateinit var mockMvc: MockMvc

    @MockkBean
    private lateinit var transferOrderOverviewService: TransferOrderOverviewService

    @Test
    fun `should return transfer order overview successfully`() {
        val transferOrderId = UUID.randomUUID()
        val mockOverviewModel = TransferOrderOverviewModel(
            transferOrderId = transferOrderId,
            transferOrderNumber = "TO-2024-001",
            skuCode = "SKU-001",
            status = "STATE_OPEN",
            destinationDcCode = "DC001",
            sourceDcCode = "DC002",
            skuName = "Test Product",
            category = "Fresh",
            skuUom = "kg",
            week = YearWeek("2024W01"),
            quantityReceived = BigDecimal("10.0"),
            casesReceived = 5,
            caseSize = BigDecimal("2.0"),
            packagingType = "case",
            priceAmount = "EUR 25.50",
            totalQuantity = BigDecimal("10.0"),
            reasonText = "Stock transfer",
            shippingMethod = "Truck",
            assignedBuyerFirstName = "John",
            assignedBuyerLastName = "Doe"
        )

        every {
            transferOrderOverviewService.getOverview(
                dcCode = "DC001",
                brand = "hellofresh",
                dcWeeks = listOf("2024W01")
            )
        } returns listOf(mockOverviewModel)

        mockMvc.perform(
            get("/transfer-orders/overview")
                .param("dc_code", "DC001")
                .param("brand", "hellofresh")
                .param("dc_weeks", "2024W01")
                .contentType(MediaType.APPLICATION_JSON)
        )
            .andExpect(status().isOk)
            .andExpect(jsonPath("$[0].transfer_order_id").value(transferOrderId.toString()))
            .andExpect(jsonPath("$[0].transfer_order_number").value("TO-2024-001"))
            .andExpect(jsonPath("$[0].sku_code").value("SKU-001"))
            .andExpect(jsonPath("$[0].status").value("STATE_OPEN"))
            .andExpect(jsonPath("$[0].destination_dc_code").value("DC001"))
            .andExpect(jsonPath("$[0].source_dc_code").value("DC002"))
            .andExpect(jsonPath("$[0].packaging_type").value("case"))
    }

    @Test
    fun `should return empty list when no transfer orders found`() {
        every {
            transferOrderOverviewService.getOverview(
                dcCode = "DC001",
                brand = "hellofresh",
                dcWeeks = listOf("2024W01")
            )
        } returns emptyList()

        mockMvc.perform(
            get("/transfer-orders/overview")
                .param("dc_code", "DC001")
                .param("brand", "hellofresh")
                .param("dc_weeks", "2024W01")
                .contentType(MediaType.APPLICATION_JSON)
        )
            .andExpect(status().isOk)
            .andExpect(jsonPath("$").isArray)
            .andExpect(jsonPath("$").isEmpty)
    }

    @Test
    fun `should handle multiple dc weeks`() {
        val transferOrderId = UUID.randomUUID()
        val mockOverviewModel = TransferOrderOverviewModel(
            transferOrderId = transferOrderId,
            transferOrderNumber = "TO-2024-002",
            skuCode = "SKU-002",
            status = "STATE_DELIVERED",
            destinationDcCode = "DC003",
            sourceDcCode = "DC004",
            skuName = "Another Product",
            category = "Protein",
            skuUom = "piece",
            week = YearWeek("2024W02"),
            quantityReceived = BigDecimal("20.0"),
            casesReceived = 10,
            caseSize = BigDecimal("2.0"),
            packagingType = "unit",
            priceAmount = "EUR 50.00",
            totalQuantity = BigDecimal("20.0"),
            reasonText = "Emergency transfer",
            shippingMethod = "Express",
            assignedBuyerFirstName = "Jane",
            assignedBuyerLastName = "Smith"
        )

        every {
            transferOrderOverviewService.getOverview(
                dcCode = null,
                brand = "hellofresh",
                dcWeeks = listOf("2024W01", "2024W02")
            )
        } returns listOf(mockOverviewModel)

        mockMvc.perform(
            get("/transfer-orders/overview")
                .param("brand", "hellofresh")
                .param("dc_weeks", "2024W01", "2024W02")
                .contentType(MediaType.APPLICATION_JSON)
        )
            .andExpect(status().isOk)
            .andExpect(jsonPath("$[0].transfer_order_id").value(transferOrderId.toString()))
            .andExpect(jsonPath("$[0].status").value("STATE_DELIVERED"))
            .andExpect(jsonPath("$[0].packaging_type").value("unit"))
    }

    @Test
    fun `should return bad request when required parameters are missing`() {
        mockMvc.perform(
            get("/transfer-orders/overview")
                .param("dc_code", "DC001")
                .contentType(MediaType.APPLICATION_JSON)
        )
            .andExpect(status().isBadRequest)
    }
}
