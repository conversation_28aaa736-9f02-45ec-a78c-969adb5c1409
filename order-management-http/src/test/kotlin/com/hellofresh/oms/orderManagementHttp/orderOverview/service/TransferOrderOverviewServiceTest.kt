package com.hellofresh.oms.orderManagementHttp.orderOverview.service

import com.hellofresh.oms.model.YearWeek
import com.hellofresh.oms.orderManagementHttp.orderOverview.config.PoOverviewConfigs
import com.hellofresh.oms.orderManagementHttp.orderOverview.out.transferOrder.TransferOrderStatusDetails
import com.hellofresh.oms.orderManagementHttp.orderOverview.out.transferOrder.TransferOrderStatusViewRepository
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import java.math.BigDecimal
import java.util.UUID

class TransferOrderOverviewServiceTest {

    private val poOverviewConfigs = mockk<PoOverviewConfigs>()
    private val transferOrderStatusViewRepository = mockk<TransferOrderStatusViewRepository>()
    
    private val transferOrderOverviewService = TransferOrderOverviewService(
        poOverviewConfigs = poOverviewConfigs,
        transferOrderStatusViewRepository = transferOrderStatusViewRepository
    )

    @Test
    fun `should get overview with specific dc code`() {
        val transferOrderId = UUID.randomUUID()
        val mockStatusDetails = TransferOrderStatusDetails(
            transferOrderId = transferOrderId,
            transferOrderNumber = "TO-2024-001",
            skuCode = "SKU-001",
            status = "STATE_OPEN",
            destinationDcCode = "DC001",
            sourceDcCode = "DC002",
            skuName = "Test Product",
            category = "Fresh",
            skuUom = "kg",
            week = YearWeek("2024W01"),
            quantityReceived = BigDecimal("10.0"),
            casesReceived = 5,
            caseSize = BigDecimal("2.0"),
            packagingType = "case",
            priceAmount = "EUR 25.50",
            totalQuantity = BigDecimal("10.0"),
            reasonText = "Stock transfer",
            shippingMethod = "Truck",
            assignedBuyerFirstName = "John",
            assignedBuyerLastName = "Doe"
        )

        every {
            transferOrderStatusViewRepository.findAllTransferOrderDetails(
                dcCodes = listOf("DC001"),
                dcWeeks = listOf("2024W01")
            )
        } returns listOf(mockStatusDetails)

        val result = transferOrderOverviewService.getOverview(
            dcCode = "DC001",
            brand = "hellofresh",
            dcWeeks = listOf("2024W01")
        )

        assertEquals(1, result.size)
        assertEquals(transferOrderId, result[0].transferOrderId)
        assertEquals("TO-2024-001", result[0].transferOrderNumber)
        assertEquals("SKU-001", result[0].skuCode)
        assertEquals("STATE_OPEN", result[0].status)

        verify {
            transferOrderStatusViewRepository.findAllTransferOrderDetails(
                dcCodes = listOf("DC001"),
                dcWeeks = listOf("2024W01")
            )
        }
    }

    @Test
    fun `should get overview for all brand dcs when dc code is null`() {
        val brandDcConfig = mapOf(
            "hellofresh" to PoOverviewConfigs.BrandDcConfig(
                dcs = listOf("DC001", "DC002", "DC003")
            )
        )

        every { poOverviewConfigs.brandDcConfig } returns brandDcConfig

        val transferOrderId = UUID.randomUUID()
        val mockStatusDetails = TransferOrderStatusDetails(
            transferOrderId = transferOrderId,
            transferOrderNumber = "TO-2024-002",
            skuCode = "SKU-002",
            status = "STATE_DELIVERED",
            destinationDcCode = "DC002",
            sourceDcCode = "DC001",
            skuName = "Another Product",
            category = "Protein",
            skuUom = "piece",
            week = YearWeek("2024W02"),
            quantityReceived = BigDecimal("20.0"),
            casesReceived = 10,
            caseSize = BigDecimal("2.0"),
            packagingType = "unit",
            priceAmount = "EUR 50.00",
            totalQuantity = BigDecimal("20.0"),
            reasonText = "Emergency transfer",
            shippingMethod = "Express",
            assignedBuyerFirstName = "Jane",
            assignedBuyerLastName = "Smith"
        )

        every {
            transferOrderStatusViewRepository.findAllTransferOrderDetails(
                dcCodes = listOf("DC001", "DC002", "DC003"),
                dcWeeks = listOf("2024W02")
            )
        } returns listOf(mockStatusDetails)

        val result = transferOrderOverviewService.getOverview(
            dcCode = null,
            brand = "hellofresh",
            dcWeeks = listOf("2024W02")
        )

        assertEquals(1, result.size)
        assertEquals(transferOrderId, result[0].transferOrderId)
        assertEquals("STATE_DELIVERED", result[0].status)
        assertEquals("unit", result[0].packagingType)

        verify {
            transferOrderStatusViewRepository.findAllTransferOrderDetails(
                dcCodes = listOf("DC001", "DC002", "DC003"),
                dcWeeks = listOf("2024W02")
            )
        }
    }

    @Test
    fun `should return empty list when no transfer orders found`() {
        every {
            transferOrderStatusViewRepository.findAllTransferOrderDetails(
                dcCodes = listOf("DC001"),
                dcWeeks = listOf("2024W01")
            )
        } returns emptyList()

        val result = transferOrderOverviewService.getOverview(
            dcCode = "DC001",
            brand = "hellofresh",
            dcWeeks = listOf("2024W01")
        )

        assertEquals(0, result.size)
    }

    @Test
    fun `should handle multiple dc weeks`() {
        val transferOrderId1 = UUID.randomUUID()
        val transferOrderId2 = UUID.randomUUID()
        
        val mockStatusDetails = listOf(
            TransferOrderStatusDetails(
                transferOrderId = transferOrderId1,
                transferOrderNumber = "TO-2024-001",
                skuCode = "SKU-001",
                status = "STATE_OPEN",
                destinationDcCode = "DC001",
                sourceDcCode = "DC002",
                skuName = "Product 1",
                category = "Fresh",
                skuUom = "kg",
                week = YearWeek("2024W01"),
                quantityReceived = BigDecimal("10.0"),
                casesReceived = 5,
                caseSize = BigDecimal("2.0"),
                packagingType = "case",
                priceAmount = "EUR 25.50",
                totalQuantity = BigDecimal("10.0"),
                reasonText = "Stock transfer",
                shippingMethod = "Truck",
                assignedBuyerFirstName = "John",
                assignedBuyerLastName = "Doe"
            ),
            TransferOrderStatusDetails(
                transferOrderId = transferOrderId2,
                transferOrderNumber = "TO-2024-002",
                skuCode = "SKU-002",
                status = "STATE_DELIVERED",
                destinationDcCode = "DC001",
                sourceDcCode = "DC003",
                skuName = "Product 2",
                category = "Protein",
                skuUom = "piece",
                week = YearWeek("2024W02"),
                quantityReceived = BigDecimal("15.0"),
                casesReceived = 8,
                caseSize = BigDecimal("1.5"),
                packagingType = "unit",
                priceAmount = "EUR 30.00",
                totalQuantity = BigDecimal("15.0"),
                reasonText = "Emergency transfer",
                shippingMethod = "Express",
                assignedBuyerFirstName = "Jane",
                assignedBuyerLastName = "Smith"
            )
        )

        every {
            transferOrderStatusViewRepository.findAllTransferOrderDetails(
                dcCodes = listOf("DC001"),
                dcWeeks = listOf("2024W01", "2024W02")
            )
        } returns mockStatusDetails

        val result = transferOrderOverviewService.getOverview(
            dcCode = "DC001",
            brand = "hellofresh",
            dcWeeks = listOf("2024W01", "2024W02")
        )

        assertEquals(2, result.size)
        assertEquals(transferOrderId1, result[0].transferOrderId)
        assertEquals(transferOrderId2, result[1].transferOrderId)
    }
}
