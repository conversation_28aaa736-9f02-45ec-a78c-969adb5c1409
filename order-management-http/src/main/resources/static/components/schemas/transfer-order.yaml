TransferOrdersOverviewResponse:
  type: array
  items:
    $ref: "#/TransferOrdersOverviewResponseInner"

TransferOrdersOverviewResponseInner:
  type: object
  required:
    - sku_code
    - source_dc_code
    - sku_name
    - category
    - packaging_type
    - total_ordered_quantity
    - total_received_quantity
    - total_cases_received
    - total_price_ordered
    - total_price_received
    - weighted_avg_case_price
    - weighted_avg_case_size
    - weighted_avg_case_size_received
    - transfer_order_breakdown
  properties:
    sku_code:
      type: string
      description: "SKU code"
    source_dc_code:
      type: string
      description: "Source distribution center code"
    sku_name:
      type: string
      description: "SKU name"
    category:
      type: string
      description: "Product category"
    sku_uom:
      type: string
      description: "SKU unit of measure"
      nullable: true
    packaging_type:
      type: string
      description: "Packaging type (case or unit)"
      enum:
        - case
        - unit
    total_ordered_quantity:
      type: string
      description: "Total ordered quantity (sum of all TOs)"
    total_received_quantity:
      type: string
      description: "Total received quantity (sum of all TOs)"
      nullable: true
    total_cases_received:
      type: integer
      description: "Total number of cases received (sum of all TOs)"
      nullable: true
    total_price_ordered:
      type: string
      description: "Total price for ordered quantities"
    total_price_received:
      type: string
      description: "Total price for received quantities"
      nullable: true
    weighted_avg_case_price:
      type: number
      format: decimal
      description: "Weighted average case price"
      nullable: true
    weighted_avg_case_size:
      type: number
      format: decimal
      description: "Weighted average case size"
      nullable: true
    weighted_avg_case_size_received:
      type: number
      format: decimal
      description: "Weighted average case size received"
      nullable: true
    assigned_buyer_first_name:
      type: string
      description: "Assigned buyer first name"
      nullable: true
    assigned_buyer_last_name:
      type: string
      description: "Assigned buyer last name"
      nullable: true
    transfer_order_breakdown:
      type: array
      description: "List of individual transfer orders in this group"
      items:
        $ref: "#/TransferOrderBreakdownItem"

TransferOrderBreakdownItem:
  type: object
  required:
    - transfer_order_id
    - transfer_order_number
    - status
    - destination_dc_code
    - week
    - total_quantity
    - case_price
  properties:
    transfer_order_id:
      type: string
      format: uuid
      description: "Transfer order unique identifier"
    transfer_order_number:
      type: string
      description: "Transfer order number"
    status:
      type: string
      description: "Transfer order status"
      enum:
        - STATE_UNSPECIFIED
        - STATE_OPEN
        - STATE_DELETED
        - STATE_CANCELLED
        - STATE_ORDERED
        - STATE_ACCEPTED
        - STATE_REJECTED
        - STATE_RESERVED
        - STATE_IN_TRANSIT
        - STATE_DELIVERED
    destination_dc_code:
      type: string
      description: "Destination distribution center code"
    week:
      type: string
      description: "Year week (e.g., 2024W01)"
    quantity_received:
      type: integer
      format: integer
      description: "Quantity received for this TO"
      nullable: true
    cases_received:
      type: integer
      description: "Number of cases received for this TO"
      nullable: true
    case_size:
      type: integer
      format: integer
      description: "Case size for this TO"
      nullable: true
    total_quantity:
      type: integer
      format: integer
      description: "Total ordered quantity for this TO"
      nullable: true
    case_price:
      type: integer
      format: integer
      description: "Price amount for this TO"
      nullable: true
    reason_text:
      type: string
      description: "Reason for transfer"
      nullable: true
    shipping_method:
      type: string
      description: "Shipping method"
      nullable: true
