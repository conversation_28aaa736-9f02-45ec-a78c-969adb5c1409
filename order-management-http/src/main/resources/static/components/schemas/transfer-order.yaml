TransferOrdersOverviewResponse:
  type: array
  items:
    $ref: "#/TransferOrdersOverviewResponseInner"

TransferOrdersOverviewResponseInner:
  type: object
  required:
    - transfer_order_id
    - transfer_order_number
    - sku_code
    - status
    - destination_dc_code
    - source_dc_code
    - sku_name
    - category
    - week
    - packaging_type
    - price_amount
    - total_quantity
  properties:
    transfer_order_id:
      type: string
      format: uuid
      description: "Transfer order unique identifier"
    transfer_order_number:
      type: string
      description: "Transfer order number"
    sku_code:
      type: string
      description: "SKU code"
    status:
      type: string
      description: "Transfer order status"
      enum:
        - STATE_UNSPECIFIED
        - STATE_OPEN
        - STATE_DELETED
        - STATE_CANCELLED
        - STATE_ORDERED
        - STATE_ACCEPTED
        - STATE_REJECTED
        - STATE_RESERVED
        - STATE_IN_TRANSIT
        - STATE_DELIVERED
    destination_dc_code:
      type: string
      description: "Destination distribution center code"
    source_dc_code:
      type: string
      description: "Source distribution center code"
    sku_name:
      type: string
      description: "SKU name"
    category:
      type: string
      description: "Product category"
    sku_uom:
      type: string
      description: "SKU unit of measure"
      nullable: true
    week:
      type: string
      description: "Year week (e.g., 2024W01)"
    quantity_received:
      type: number
      format: decimal
      description: "Quantity received"
      nullable: true
    cases_received:
      type: integer
      description: "Number of cases received"
      nullable: true
    case_size:
      type: number
      format: decimal
      description: "Case size"
      nullable: true
    packaging_type:
      type: string
      description: "Packaging type (case or unit)"
      enum:
        - case
        - unit
    price_amount:
      type: string
      description: "Price amount with currency"
    total_quantity:
      type: number
      format: decimal
      description: "Total quantity"
    reason_text:
      type: string
      description: "Reason for transfer"
      nullable: true
    shipping_method:
      type: string
      description: "Shipping method"
      nullable: true
    assigned_buyer_first_name:
      type: string
      description: "Assigned buyer first name"
      nullable: true
    assigned_buyer_last_name:
      type: string
      description: "Assigned buyer last name"
      nullable: true
