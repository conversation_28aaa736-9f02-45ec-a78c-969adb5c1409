package com.hellofresh.oms.orderManagementHttp.imports.validator

import com.hellofresh.oms.model.DistributionCenter
import com.hellofresh.oms.model.EmergencyReason
import com.hellofresh.oms.model.PackagingType
import com.hellofresh.oms.model.ShipMethodEnum
import com.hellofresh.oms.model.Sku
import com.hellofresh.oms.model.UOM
import com.hellofresh.oms.model.YearWeek
import com.hellofresh.oms.model.supplier.Supplier
import com.hellofresh.oms.orderManagement.generated.api.model.DeliveryWindowDto
import com.hellofresh.oms.orderManagementHttp.imports.validator.BatchCreatePurchaseOrderValidator.LineValidationError
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime

data class BatchOrderRowValidatedDto(
    val rowNumber: Int,
    val yearWeek: YearWeek,
    val distributionCenter: DistributionCenter,
    val emergencyReason: EmergencyReason,
    val supplier: Supplier,
    val deliveryWindow: DeliveryWindowDto,
    val sku: Sku,
    val orderSize: BigDecimal,
    val bufferValue: BigDecimal,
    val caseSize: BigDecimal,
    val price: BigDecimal,
    val packagingType: PackagingType,
    val uom: UOM,
    val shipMethod: ShipMethodEnum,
    val comments: String?,
)

data class BatchOrderRowResultDto(
    val rowNumber: Int,
    val yearWeek: Result<YearWeek>,
    val distributionCenter: Result<DistributionCenter>,
    val emergencyReason: Result<EmergencyReason>,
    val supplier: Result<Supplier>,
    val deliveryDate: Result<LocalDate>,
    val startTime: Result<LocalTime>,
    val endTime: Result<LocalTime>,
    val sku: Result<Sku>,
    val orderSize: Result<BigDecimal>,
    val bufferValue: Result<BigDecimal>,
    val caseSize: Result<BigDecimal>,
    val price: Result<BigDecimal>,
    val packagingType: Result<BatchRowPackagingType>,
    val uom: Result<BatchRowUom>,
    val shipMethod: Result<BatchRowShipMethod>,
    val comments: Result<String>,
) {
    private fun collectAllResults() = setOf(
        yearWeek,
        distributionCenter,
        emergencyReason,
        supplier,
        deliveryDate,
        startTime,
        endTime,
        sku,
        orderSize,
        bufferValue,
        caseSize,
        price,
        packagingType,
        uom,
        shipMethod,
        comments,
    )

    fun collectErrors() = collectAllResults()
        .mapNotNull { it.exceptionOrNull() }
        .filterIsInstance<LineValidationError>()
        .toSet()

    fun toDto() = BatchOrderRowValidatedDto(
        rowNumber = rowNumber,
        yearWeek = yearWeek.getOrThrow(),
        distributionCenter = distributionCenter.getOrThrow(),
        emergencyReason = emergencyReason.getOrThrow(),
        supplier = supplier.getOrThrow(),
        /**
         * According to BulkEpoView->getStartDate()/getEndDate() we don't need to consider end of the day
         * while combining the deliveryDate with start and end time.
         *
         * 2025-03-21 - We've decided to break with Tapioca behaviour, and actually handle delivery windows
         * spanning the end-of-day.
         */
        deliveryWindow = DeliveryWindowDto(
            start = LocalDateTime.of(deliveryDate.getOrThrow(), startTime.getOrThrow()),
            end = if (endTime.getOrThrow().isBefore(startTime.getOrThrow())) {
                LocalDateTime.of(deliveryDate.getOrThrow().plusDays(1), endTime.getOrThrow())
            } else {
                LocalDateTime.of(deliveryDate.getOrThrow(), endTime.getOrThrow())
            },
        ),
        sku = sku.getOrThrow(),
        orderSize = orderSize.getOrThrow(),
        bufferValue = bufferValue.getOrThrow(),
        caseSize = caseSize.getOrThrow(),
        price = price.getOrThrow(),
        packagingType = packagingType.getOrThrow().toPackagingType(),
        uom = uom.getOrThrow().toUOM(),
        shipMethod = shipMethod.getOrThrow().toShipMethodEnum(),
        comments = comments.getOrNull(),
    )

    fun hasErrors() = collectAllResults().any { it.isFailure }
}

/**
 * We need this class to make the transition from OT to OM easier,
 * as we need to map the packaging type from OT to OM internal type.
 */
enum class BatchRowUom(val value: String, override val alias: Boolean = false) : ImportEnum {
    OZ("oz"),
    KG("kg"),
    LBS("lbs"),
    L("L"),
    GAL("gal"),
    UNIT("unit", alias = true),
    UNITS("units", alias = true),
    UNIT_S("unit(s)");

    override fun toString(): String = value
    fun toUOM(): UOM = when (this) {
        OZ -> UOM.OZ
        KG -> UOM.KG
        LBS -> UOM.LBS
        L -> UOM.L
        GAL -> UOM.GAL
        UNIT -> UOM.UNIT
        UNITS -> UOM.UNIT
        UNIT_S -> UOM.UNIT
    }
}

interface ImportEnum {
    val alias: Boolean
}

/**
 * We need this class to make the transition from OT to OM easier,
 * as we need to map the ship method from OT to OM internal type.
 */
enum class BatchRowShipMethod(val value: String, override val alias: Boolean = false) : ImportEnum {
    OTHER("Other"),
    OTHERS("Others", alias = true),
    FREIGHT_ON_BOARD("Freight On Board"),
    CROSSDOCK("Crossdock"),
    VENDOR_DELIVERED("Vendor Delivered");

    override fun toString(): String = value
    fun toShipMethodEnum(): ShipMethodEnum = when (this) {
        OTHER -> ShipMethodEnum.OTHER
        OTHERS -> ShipMethodEnum.OTHER
        FREIGHT_ON_BOARD -> ShipMethodEnum.FREIGHT_ON_BOARD
        CROSSDOCK -> ShipMethodEnum.CROSSDOCK
        VENDOR_DELIVERED -> ShipMethodEnum.VENDOR
    }
}

/**
 * PALLET_TYPE is not supported at the moment in OT and will not be supported for now.
 */
enum class BatchRowPackagingType(override val alias: Boolean = false) : ImportEnum {
    UNIT_TYPE,
    CASE_TYPE;

    fun toPackagingType(): PackagingType = when (this) {
        UNIT_TYPE -> PackagingType.UNIT_TYPE
        CASE_TYPE -> PackagingType.CASE_TYPE
    }
}
