package com.hellofresh.oms.orderManagementHttp.supplierSku.intake

import com.hellofresh.oms.orderManagement.generated.api.model.PageResultApiResponse
import com.hellofresh.oms.orderManagement.generated.api.model.PriceMoneyDto
import com.hellofresh.oms.orderManagement.generated.api.model.SortApiResponse
import com.hellofresh.oms.orderManagement.generated.api.model.SupplierSkuApiResponse
import com.hellofresh.oms.orderManagement.generated.api.model.SupplierSkuApiResponse.Uom
import com.hellofresh.oms.orderManagement.generated.api.model.SupplierSkusListApiResponse
import com.hellofresh.oms.orderManagementHttp.supplierSku.out.SkuWithPrice
import com.hellofresh.oms.orderManagementHttp.supplierSku.service.domain.SupplierSkuDto

fun SupplierSkuDto.toSupplierSkuApiResponse() = SupplierSkusListApiResponse(
    pageResult = PageResultApiResponse(
        number = this.pageNumber,
        pageSize = this.pageSize,
        totalElements = this.totalElements.toBigDecimal(),
        totalPages = this.totalPages,
        sort = this.sort.get().map {
            SortApiResponse(
                property = it.property,
                direction = SortApiResponse.Direction.valueOf(it.direction.name),
            )
        }
            .toList(),
    ),
    items = this.supplierSkus.map {
        SupplierSkuApiResponse(
            id = it.id,
            code = it.code,
            status = it.status?.name,
            skuCategory = it.skuCategory,
            name = it.name,
            caseSize = it.caseSize,
            price = it.resolvePrice(),
            supplierUuid = it.supplierId,
            buffer = it.buffer?.toPercent(),
            casesPerPallet = it.casesPerPallet,
            uom = it.uom?.let { uom -> Uom.valueOf(uom.uppercase()) }
        )
    },
)

private fun SkuWithPrice.resolvePrice(): PriceMoneyDto? {
    if (this.pricePermyriad == null || this.currency == null || this.priceType == null) {
        return null
    }

    return PriceMoneyDto(
        amount = this.pricePermyriad.convertToBigDecimal(scale = 3).toString(),
        currency = this.currency,
        type = this.priceType,
    )
}
