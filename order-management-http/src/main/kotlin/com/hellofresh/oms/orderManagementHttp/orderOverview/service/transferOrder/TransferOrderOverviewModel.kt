package com.hellofresh.oms.orderManagementHttp.orderOverview.service.transferOrder

import com.hellofresh.oms.model.YearWeek
import java.math.BigDecimal
import java.util.UUID

data class TransferOrderOverviewModel(
    val skuCode: String,
    val sourceDcCode: String,
    val skuName: String,
    val category: String,
    val skuUom: String? = null,
    val packagingType: String,
    val totalOrderedQuantity: Int,
    val totalReceivedQuantity: Int? = null,
    val totalCasesReceived: Int? = null,
    val totalPriceOrdered: Int,
    val totalPriceReceived: Int? = null,
    val weightedAvgCasePrice: BigDecimal? = null,
    val weightedAvgCaseSize: BigDecimal? = null,
    val weightedAvgCaseSizeReceived: BigDecimal? = null,
    val assignedBuyerFirstName: String? = null,
    val assignedBuyerLastName: String? = null,
    val transferOrderBreakdown: List<TransferOrderBreakdownItem>
)

data class TransferOrderBreakdownItem(
    val transferOrderId: UUID,
    val transferOrderNumber: String,
    val status: String,
    val destinationDcCode: String,
    val week: YearWeek,
    val quantityReceived: BigDecimal? = null,
    val casesReceived: Int? = null,
    val caseSize: BigDecimal? = null,
    val totalQuantity: BigDecimal,
    val priceAmount: String,
    val reasonText: String? = null,
    val shippingMethod: String? = null
)
