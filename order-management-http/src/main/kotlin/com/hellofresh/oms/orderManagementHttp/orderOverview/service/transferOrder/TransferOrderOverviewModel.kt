package com.hellofresh.oms.orderManagementHttp.orderOverview.service.transferOrder

import com.hellofresh.oms.model.YearWeek
import java.math.BigDecimal
import java.util.UUID

data class TransferOrderOverviewModel(
    val transferOrderId: UUID,
    val transferOrderNumber: String,
    val skuCode: String,
    val status: String,
    val destinationDcCode: String,
    val sourceDcCode: String,
    val skuName: String,
    val category: String,
    val skuUom: String? = null,
    val week: YearWeek,
    val quantityReceived: BigDecimal? = null,
    val casesReceived: Int? = null,
    val caseSize: BigDecimal? = null,
    val packagingType: String,
    val priceAmount: String,
    val totalQuantity: BigDecimal,
    val reasonText: String? = null,
    val shippingMethod: String? = null,
    val assignedBuyerFirstName: String? = null,
    val assignedBuyerLastName: String? = null
)
