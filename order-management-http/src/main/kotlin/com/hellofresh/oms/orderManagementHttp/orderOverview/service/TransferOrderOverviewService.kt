package com.hellofresh.oms.orderManagementHttp.orderOverview.service

import com.hellofresh.oms.orderManagementHttp.orderOverview.config.PoOverviewConfigs
import com.hellofresh.oms.orderManagementHttp.orderOverview.out.transferOrder.TransferOrderStatusDetails
import com.hellofresh.oms.orderManagementHttp.orderOverview.out.transferOrder.TransferOrderStatusViewRepository
import com.hellofresh.oms.orderManagementHttp.orderOverview.service.transferOrder.TransferOrderOverviewModel
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import java.math.BigDecimal

@Service
class TransferOrderOverviewService(
    private val poOverviewConfigs: PoOverviewConfigs,
    private val transferOrderStatusViewRepository: TransferOrderStatusViewRepository
) {
    fun getOverview(
        dcCode: String?,
        brand: String,
        dcWeeks: List<String>
    ): List<TransferOrderOverviewModel> {
        logger.debug("Getting transfer order overview for dcCode: {}, dcWeeks: {}, brand: {}", dcCode, dcWeeks, brand)

        val dcCodes: List<String> = if (dcCode.isNullOrEmpty()) {
            poOverviewConfigs.brandDcConfig.filter { it.key == brand }
                .flatMap { it.value.dcs }
        } else {
            listOf(dcCode)
        }

        val transferOrderDetails = transferOrderStatusViewRepository.findAllTransferOrderDetails(dcCodes, dcWeeks)

        return transferOrderDetails
            .groupBy { "${it.sourceDcCode}-${it.skuCode}" }
            .map { (_, groupedDetails) ->
                aggregateTransferOrderGroup(groupedDetails)
            }
    }

    private fun aggregateTransferOrderGroup(groupedDetails: List<TransferOrderStatusDetails>): TransferOrderOverviewModel {
        val firstDetail = groupedDetails.first()

        val totalOrderedQuantity = groupedDetails.sumOf { it.quantityOrdered }
        val totalReceivedQuantity = groupedDetails.map { it.quantityReceived }.takeIf { it.isNotEmpty() }?.sumOf { it }
        val totalCasesReceived = groupedDetails.mapNotNull { it.casesReceived }.takeIf { it.isNotEmpty() }?.sum()

        val totalQuantityForWeighting = totalOrderedQuantity
        val weightedAvgCaseSize = if (totalQuantityForWeighting > BigDecimal.ZERO) {
            groupedDetails.mapNotNull { detail ->
                detail.caseSize?.let { caseSize ->
                    val weight = detail.quantityOrdered / totalQuantityForWeighting
                    caseSize * weight
                }
            }.takeIf { it.isNotEmpty() }?.sumOf { it }
        } else null

        val weightedAvgCaseSizeReceived = if (totalReceivedQuantity != null && totalReceivedQuantity > BigDecimal.ZERO) {
            groupedDetails.mapNotNull { detail ->
                detail.caseSize?.let { caseSize ->
                    val receivedQty = detail.quantityReceived
                    if (receivedQty > BigDecimal.ZERO) {
                        val weight = receivedQty / totalReceivedQuantity
                        caseSize * weight
                    } else null
                }
            }.takeIf { it.isNotEmpty() }?.sumOf { it }
        } else null

        val totalPriceOrdered = groupedDetails.sumOf { it.quantityOrdered }
        val totalPriceReceived = groupedDetails.sumOf { it.quantityReceived }
        val weightedAvgCasePrice = groupedDetails.sumOf { it.casePrice ?: BigDecimal.ZERO }

        val breakdown = groupedDetails.map { it.mapToTransferOrderBreakdownItem() }

        return TransferOrderOverviewModel(
            skuCode = firstDetail.skuCode,
            sourceDcCode = firstDetail.sourceDcCode,
            skuName = firstDetail.skuName,
            category = firstDetail.category,
            skuUom = firstDetail.skuUom,
            packagingType = firstDetail.orderUnit,
            totalOrderedQuantity = totalOrderedQuantity,
            totalReceivedQuantity = totalReceivedQuantity,
            totalCasesReceived = totalCasesReceived,
            totalPriceOrdered = totalPriceOrdered,
            totalPriceReceived = totalPriceReceived,
            weightedAvgCasePrice = weightedAvgCasePrice,
            weightedAvgCaseSize = weightedAvgCaseSize,
            weightedAvgCaseSizeReceived = weightedAvgCaseSizeReceived,
            assignedBuyerFirstName = firstDetail.assignedBuyerFirstName,
            assignedBuyerLastName = firstDetail.assignedBuyerLastName,
            transferOrderBreakdown = breakdown
        )
    }


    companion object {
        private val logger = LoggerFactory.getLogger(this::class.java)
    }
}
