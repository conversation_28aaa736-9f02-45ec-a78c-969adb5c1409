package com.hellofresh.oms.orderManagementHttp.orderOverview.service

import com.hellofresh.oms.orderManagementHttp.orderOverview.out.transferOrder.TransferOrderStatusDetails
import com.hellofresh.oms.orderManagement.generated.api.model.TransferOrdersOverviewResponseInner
import com.hellofresh.oms.orderManagementHttp.orderOverview.service.transferOrder.TransferOrderOverviewModel

fun TransferOrderOverviewModel.mapToTransferOrderOverviewModelToApiResponse(): TransferOrdersOverviewResponseInner =
    TransferOrdersOverviewResponseInner(
        transferOrderId = this.transferOrderId,
        transferOrderNumber = this.transferOrderNumber,
        skuCode = this.skuCode,
        status = TransferOrdersOverviewResponseInner.Status.forValue(this.status),
        destinationDcCode = this.destinationDcCode,
        sourceDcCode = this.sourceDcCode,
        skuName = this.skuName,
        category = this.category,
        skuUom = this.skuUom,
        week = this.week.value,
        quantityReceived = this.quantityReceived,
        casesReceived = this.casesReceived,
        caseSize = this.caseSize,
        packagingType = TransferOrdersOverviewResponseInner.PackagingType.forValue(this.packagingType),
        priceAmount = this.priceAmount,
        totalQuantity = this.totalQuantity,
        reasonText = this.reasonText,
        shippingMethod = this.shippingMethod,
        assignedBuyerFirstName = this.assignedBuyerFirstName,
        assignedBuyerLastName = this.assignedBuyerLastName
    )

fun TransferOrderStatusDetails.mapToTransferOrderOverviewModel(): TransferOrderOverviewModel =
    TransferOrderOverviewModel(
        transferOrderId = this.transferOrderId,
        transferOrderNumber = this.transferOrderNumber,
        skuCode = this.skuCode,
        status = this.status,
        destinationDcCode = this.destinationDcCode,
        sourceDcCode = this.sourceDcCode,
        skuName = this.skuName,
        category = this.category,
        skuUom = this.skuUom,
        week = this.week,
        quantityReceived = this.quantityReceived,
        casesReceived = this.casesReceived,
        caseSize = this.caseSize,
        packagingType = this.packagingType,
        priceAmount = this.priceAmount,
        totalQuantity = this.totalQuantity,
        reasonText = this.reasonText,
        shippingMethod = this.shippingMethod,
        assignedBuyerFirstName = this.assignedBuyerFirstName,
        assignedBuyerLastName = this.assignedBuyerLastName
    )
