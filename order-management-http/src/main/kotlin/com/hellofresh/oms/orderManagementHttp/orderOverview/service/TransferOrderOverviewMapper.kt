package com.hellofresh.oms.orderManagementHttp.orderOverview.service

import com.hellofresh.oms.model.YearWeek
import com.hellofresh.oms.orderManagement.generated.api.model.TransferOrdersOverviewResponseInner
import com.hellofresh.oms.orderManagementHttp.orderOverview.out.TransferOrderStatusDetails
import com.hellofresh.oms.orderManagementHttp.orderOverview.service.transferOrder.TransferOrderOverviewModel
import java.math.BigDecimal

fun TransferOrderOverviewModel.mapToTransferOrderOverviewModelToApiResponse(): TransferOrdersOverviewResponseInner =
    TransferOrdersOverviewResponseInner(
        transferOrderId = this.transferOrderId,
        transferOrderNumber = this.transferOrderNumber,
        skuCode = this.skuCode,
        status = this.status,
        destinationDcCode = this.destinationDcCode,
        sourceDcCode = this.sourceDcCode,
        skuName = this.skuName,
        category = this.category,
        skuUom = this.skuUom,
        week = this.week.value,
        quantityReceived = this.quantityReceived,
        casesReceived = this.casesReceived,
        caseSize = this.caseSize,
        packagingType = this.packagingType,
        priceAmount = this.priceAmount,
        totalQuantity = this.totalQuantity,
        reasonText = this.reasonText,
        shippingMethod = this.shippingMethod,
        assignedBuyerFirstName = this.assignedBuyerFirstName,
        assignedBuyerLastName = this.assignedBuyerLastName
    )

fun TransferOrderStatusDetails.mapToTransferOrderOverviewModel(brand: String): TransferOrderOverviewModel =
    TransferOrderOverviewModel(
        transferOrderId = this.transferOrderId,
        transferOrderNumber = this.transferOrderNumber,
        skuCode = this.skuCode ?: "",
        status = this.status,
        destinationDcCode = this.destinationDcCode,
        sourceDcCode = this.sourceDcCode,
        skuName = this.skuName ?: "",
        category = this.category ?: "Unknown",
        skuUom = this.skuUom,
        week = YearWeek(this.week),
        quantityReceived = null, // This would come from GRN data if available
        casesReceived = null, // This would come from GRN data if available
        caseSize = this.caseSize,
        packagingType = this.packagingType ?: "unit",
        priceAmount = this.priceAmount ?: "0.00",
        totalQuantity = this.totalQuantity ?: BigDecimal.ZERO,
        reasonText = this.reasonText,
        shippingMethod = this.shippingMethod,
        assignedBuyerFirstName = this.assignedBuyerFirstName,
        assignedBuyerLastName = this.assignedBuyerLastName
    )
