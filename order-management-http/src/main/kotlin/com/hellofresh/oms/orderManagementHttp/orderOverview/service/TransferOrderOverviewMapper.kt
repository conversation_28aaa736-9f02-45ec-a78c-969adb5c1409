package com.hellofresh.oms.orderManagementHttp.orderOverview.service

import com.hellofresh.oms.model.YearWeek
import com.hellofresh.oms.orderManagement.generated.api.model.TransferOrderBreakdownItem
import com.hellofresh.oms.orderManagement.generated.api.model.TransferOrdersOverviewResponseInner
import com.hellofresh.oms.orderManagementHttp.orderOverview.out.transferOrder.TransferOrderStatusDetails
import com.hellofresh.oms.orderManagementHttp.orderOverview.service.transferOrder.TransferOrderOverviewModel
import java.math.BigDecimal

fun TransferOrderOverviewModel.mapToTransferOrderOverviewModelToApiResponse(): TransferOrdersOverviewResponseInner =
    TransferOrdersOverviewResponseInner(
        skuCode = this.skuCode,
        sourceDcCode = this.sourceDcCode,
        skuName = this.skuName,
        category = this.category,
        skuUom = this.skuUom,
        packagingType = TransferOrdersOverviewResponseInner.PackagingType.forValue(this.packagingType),
        totalOrderedQuantity = this.totalOrderedQuantity.toString(),
        totalReceivedQuantity = this.totalReceivedQuantity?.toString(),
        totalCasesReceived = this.totalCasesReceived,
        totalPriceOrdered = this.totalPriceOrdered,
        totalPriceReceived = this.totalPriceReceived,
        weightedAvgCasePrice = this.weightedAvgCasePrice,
        weightedAvgCaseSize = this.weightedAvgCaseSize,
        weightedAvgCaseSizeReceived = this.weightedAvgCaseSizeReceived,
        assignedBuyerFirstName = this.assignedBuyerFirstName,
        assignedBuyerLastName = this.assignedBuyerLastName,
        transferOrderBreakdown = this.transferOrderBreakdown.map { it.mapToApiBreakdownItem() }
    )

fun com.hellofresh.oms.orderManagementHttp.orderOverview.service.transferOrder.TransferOrderBreakdownItem.mapToApiBreakdownItem(): TransferOrderBreakdownItem =
    TransferOrderBreakdownItem(
        transferOrderId = this.transferOrderId,
        transferOrderNumber = this.transferOrderNumber,
        status = TransferOrderBreakdownItem.Status.forValue(this.status),
        destinationDcCode = this.destinationDcCode,
        week = this.week.value,
        quantityReceived = this.quantityReceived?.toString(),
        casesReceived = this.casesReceived,
        caseSize = this.caseSize?.toString(),
        totalQuantity = this.totalQuantity.toString(),
        casePrice = this.casePrice,
        reasonText = this.reasonText,
        shippingMethod = this.shippingMethod
    )

fun TransferOrderStatusDetails.mapToTransferOrderBreakdownItem(): com.hellofresh.oms.orderManagementHttp.orderOverview.service.transferOrder.TransferOrderBreakdownItem =
    com.hellofresh.oms.orderManagementHttp.orderOverview.service.transferOrder.TransferOrderBreakdownItem(
        transferOrderId = this.transferOrderId,
        transferOrderNumber = this.transferOrderNumber,
        status = this.status,
        destinationDcCode = this.destinationDcCode,
        week = YearWeek(this.week),
        quantityReceived = this.quantityReceived,
        casesReceived = this.casesReceived,
        caseSize = this.caseSize,
        totalQuantity = (this.quantityOrdered ?: 0).toBigDecimal(),
        priceAmount = "EUR ${String.format("%.2f", this.casePrice ?: BigDecimal.ZERO)}",
        reasonText = this.reasonText,
        shippingMethod = this.shippingMethod
    )
