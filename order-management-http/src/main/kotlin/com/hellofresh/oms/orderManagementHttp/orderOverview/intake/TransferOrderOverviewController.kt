package com.hellofresh.oms.orderManagementHttp.orderOverview.intake

import com.hellofresh.oms.orderManagement.generated.api.model.TransferOrdersOverviewResponseInner
import com.hellofresh.oms.orderManagement.generated.api.routes.TransferOrderStatusApi
import com.hellofresh.oms.orderManagementHttp.orderOverview.service.TransferOrderOverviewService
import com.hellofresh.oms.orderManagementHttp.orderOverview.service.mapToTransferOrderOverviewModelToApiResponse
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.RestController

@RestController
class TransferOrderOverviewController(
    private val transferOrderOverviewService: TransferOrderOverviewService,
) : TransferOrderStatusApi {

    override fun transferOrderStatusOverview(
        dcWeeks: List<String>,
        brand: String,
        dcCode: String?
    ): ResponseEntity<List<TransferOrdersOverviewResponseInner>> {
        val overviewModelList = transferOrderOverviewService.getOverview(
            dcCode = dcCode,
            brand = brand,
            dcWeeks = dcWeeks,
        )
        return ResponseEntity.ok(
            overviewModelList.map { it.mapToTransferOrderOverviewModelToApiResponse() }
        )
    }
}
