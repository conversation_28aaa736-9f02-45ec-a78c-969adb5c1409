package com.hellofresh.oms.orderManagementHttp.supplierSku.service

import com.hellofresh.oms.orderManagementHttp.sku.SkuService
import com.hellofresh.oms.orderManagementHttp.supplier.SupplierRepository
import com.hellofresh.oms.orderManagementHttp.supplierSku.intake.SupplierSkuSearch
import com.hellofresh.oms.orderManagementHttp.supplierSku.out.SkuWithPrice
import com.hellofresh.oms.orderManagementHttp.supplierSku.out.SupplierSkuPackagingRepository
import com.hellofresh.oms.orderManagementHttp.supplierSku.out.SupplierSkuRepository
import com.hellofresh.oms.orderManagementHttp.supplierSku.service.domain.SupplierSkuDto
import kotlin.jvm.optionals.getOrNull
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service

@Service
class SupplierSkuService(
    private val priceService: PriceService,
    private val skuService: SkuService,
    private val supplierSkuRepository: SupplierSkuRepository,
    private val supplierRepository: SupplierRepository,
    private val supplierSkuPackagingRepository: SupplierSkuPackagingRepository
) {

    fun getSkus(supplierSkuSearch: SupplierSkuSearch): SupplierSkuDto {
        val supplier = supplierRepository.findById(supplierSkuSearch.supplierId)
            .getOrNull() ?: throw IllegalArgumentException("Supplier not found id: ${supplierSkuSearch.supplierId}")

        val supplierSkus = supplierSkuRepository.findAllByParentSupplierIdIsInAndStatusIsNot(
            supplierParentIds = listOf(supplier.parentId, supplier.id),
        )

        val supplierSkuIdToPrices = priceService.getActivePrices(
            dcCode = supplierSkuSearch.dcCode,
            deliveryDate = supplierSkuSearch.deliveryDate,
            supplierSkuIds = supplierSkus.map { it.uuid },
        )

        val skus = skuService.getSkusForMarketOrderBySupplierAssignedFirst(
            searchQuery = supplierSkuSearch,
            supplierSkuIds = supplierSkus.map { it.uuid },
        )

        val skuPackaging = supplierSkuPackagingRepository.findAllBySupplierSkuIdIn(supplierSkus.map { it.uuid })

        val items = skus.map { sku ->
            val supplierSku = supplierSkus.find { it.skuId == sku.uuid }
            val casesPerPallet = skuPackaging.find { it.supplierSkuId == supplierSku?.uuid }
            val priceDetails = supplierSkuIdToPrices[supplierSku?.uuid]
            val price = when (priceDetails?.priceType) {
                "unit_type", "repackaging_type" -> priceDetails.pricePermyriad
                "case_type" -> priceDetails.casePrice
                else -> null
            }

            if (priceDetails != null && priceDetails.currency != supplier.currency) {
                logger.error(
                    "Supplier sku with wrong currency: supplierId: ${supplier.id}, " +
                        "supplierCode: ${supplier.code}, supplierCurrency: ${supplier.currency}, " +
                        "supplierSkuId: ${supplierSku?.uuid}, supplierSkuCurrency: ${priceDetails.currency}"
                )
            }

            SkuWithPrice(
                id = sku.uuid,
                supplierId = if (supplierSku != null) supplier.id else null,
                code = sku.code,
                name = sku.name,
                status = supplierSku?.status,
                skuCategory = sku.category,
                uom = sku.uom?.name,
                caseSize = priceDetails?.caseSize,
                pricePermyriad = price,
                currency = supplier.currency,
                priceType = priceDetails?.priceType,
                casesPerPallet = casesPerPallet?.casesPerPallet,
                buffer = priceDetails?.bufferPermyriad,
            )
        }.content

        return SupplierSkuDto(
            pageNumber = skus.number,
            pageSize = skus.size,
            totalElements = skus.totalElements,
            totalPages = skus.totalPages,
            sort = skus.sort,
            supplierSkus = items,
        )
    }

    companion object {
        private val logger = LoggerFactory.getLogger(this::class.java)
    }
}
