package com.hellofresh.oms.model.transferOrder

import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.Id
import jakarta.persistence.Table
import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.UUID

@Entity
@Table(name = "transfer_order_item")
data class TransferOrderItem(
    @Id
    val id: UUID,
    
    @Column(name = "transfer_order_id")
    val transferOrderId: UUID,
    
    @Column(name = "sku_code")
    val skuCode: String,
    
    @Column(name = "sku_id")
    val skuId: UUID?,
    
    @Column(name = "requested_quantity")
    val requestedQuantity: BigDecimal,
    
    @Column(name = "approved_quantity")
    val approvedQuantity: BigDecimal?,
    
    @Column(name = "shipped_quantity")
    val shippedQuantity: BigDecimal?,
    
    @Column(name = "received_quantity")
    val receivedQuantity: BigDecimal?,
    
    @Column(name = "unit_of_measure")
    val unitOfMeasure: String?,
    
    @Column(name = "case_size")
    val caseSize: BigDecimal?,
    
    val comments: String?,
    
    @Column(name = "created_at")
    val createdAt: LocalDateTime,
    
    @Column(name = "updated_at")
    val updatedAt: LocalDateTime
)
