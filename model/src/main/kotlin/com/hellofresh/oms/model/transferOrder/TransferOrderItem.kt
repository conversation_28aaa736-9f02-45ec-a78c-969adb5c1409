package com.hellofresh.oms.model.transferOrder

import jakarta.persistence.Column
import jakarta.persistence.Embedded
import jakarta.persistence.Entity
import jakarta.persistence.Id
import jakarta.persistence.Table
import java.time.LocalDateTime
import java.util.UUID

@Entity
@Table(name = "transfer_order_item")
data class TransferOrderItem(
    @Id
    val id: UUID,

    @Column(name = "transfer_order_id")
    val transferOrderId: UUID,

    @Column(name = "csku_code")
    val cskuCode: String,

    @Column(name = "csku_name")
    val cskuName: String?,

    @Column(name = "sku_id")
    val skuId: UUID?,

    @Column(name = "supplier_id")
    val supplierId: UUID?,

    @Column(name = "supplier_code")
    val supplierCode: String?,

    @Column(name = "order_size")
    val orderSize: Int?,

    @Column(name = "inventory_type")
    val inventoryType: String?,

    @Embedded
    val price: TransferOrderItemPrice?,

    @Embedded
    val totalPrice: TransferOrderItemTotalPrice?,

    @Embedded
    val casePackaging: TransferOrderItemCasePackaging?,

    @Embedded
    val quantity: TransferOrderItemQuantity?,

    @Column(name = "created_at")
    val createdAt: LocalDateTime,

    @Column(name = "updated_at")
    val updatedAt: LocalDateTime
)

@jakarta.persistence.Embeddable
data class TransferOrderItemPrice(
    @Column(name = "price_currency_code")
    val currencyCode: String?,

    @Column(name = "price_nanos")
    val nanos: Long?
)

@jakarta.persistence.Embeddable
data class TransferOrderItemTotalPrice(
    @Column(name = "total_price_currency_code")
    val currencyCode: String?,

    @Column(name = "total_price_units")
    val units: String?,

    @Column(name = "total_price_nanos")
    val nanos: Long?
)

@jakarta.persistence.Embeddable
data class TransferOrderItemCasePackaging(
    @Column(name = "case_packaging_size")
    val size: String?,

    @Column(name = "case_packaging_unit")
    val unit: String?
)

@jakarta.persistence.Embeddable
data class TransferOrderItemQuantity(
    @Column(name = "quantity_value")
    val value: String?
)
