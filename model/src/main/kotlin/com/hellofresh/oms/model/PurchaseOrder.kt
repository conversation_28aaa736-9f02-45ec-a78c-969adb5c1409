package com.hellofresh.oms.model

import com.hellofresh.oms.model.POType.EMERGENCY
import com.hellofresh.oms.model.PurchaseOrderStatus.INITIATED
import jakarta.persistence.AttributeOverride
import jakarta.persistence.AttributeOverrides
import jakarta.persistence.CascadeType
import jakarta.persistence.Column
import jakarta.persistence.Embeddable
import jakarta.persistence.Embedded
import jakarta.persistence.Entity
import jakarta.persistence.EnumType.STRING
import jakarta.persistence.Enumerated
import jakarta.persistence.FetchType.EAGER
import jakarta.persistence.Id
import jakarta.persistence.OneToMany
import java.time.LocalDateTime
import java.util.UUID
import java.util.UUID.randomUUID

@Entity
data class PurchaseOrder(
    @Id
    val id: UUID = randomUUID(),
    val poNumber: String,
    val version: Int,

    @Enumerated(STRING)
    val type: POType,

    @AttributeOverrides(AttributeOverride(name = "value", column = Column(name = "year_week")))
    val yearWeek: YearWeek,
    val userId: UUID,
    val userEmail: String,

    @Enumerated(STRING)
    val status: PurchaseOrderStatus,
    val sendTime: LocalDateTime?,
    val supplierId: UUID,
    val supplierCode: String,
    val dcCode: String,

    @Enumerated(value = STRING)
    val shippingMethod: ShipMethodEnum,

    @Embedded
    val shippingAddress: ShippingAddress,
    val expectedStartTime: LocalDateTime,
    val expectedEndTime: LocalDateTime,

    @OneToMany(cascade = [CascadeType.ALL], fetch = EAGER, mappedBy = "poId")
    val orderItems: Set<OrderItem>,
    val emergencyReasonUuid: UUID? = null,

    @AttributeOverrides(
        AttributeOverride(name = "currency", column = Column(name = "total_price_currency")),
        AttributeOverride(name = "amount", column = Column(name = "total_price_amount")),
    )
    val totalPrice: Money,
    val comment: String? = null,
    val createdAt: LocalDateTime,
    val updatedAt: LocalDateTime?,
    val isSynced: Boolean,
    val deliveryDateChangeReasonId: UUID?,

    @Enumerated(STRING)
    val origin: Origin = Origin.OTHER,
) {
    companion object {
        /**
         * Creates a new purchase order entity from the supplied purchase order, bumping the version to +1.
         * Also creates a new set of order items using values of the order items in the supplied purchase order.
         */
        fun createNewRevisionFrom(
            persistedPurchaseOrder: PurchaseOrder,
            userId: UUID,
            userEmail: String,
            currentTime: LocalDateTime
        ): PurchaseOrder {
            val poId = randomUUID()
            return PurchaseOrder(
                id = poId,
                userId = userId,
                userEmail = userEmail,
                createdAt = currentTime,
                updatedAt = currentTime,
                version = persistedPurchaseOrder.version + 1,
                sendTime = null,
                status = INITIATED,
                orderItems = persistedPurchaseOrder.orderItems.map {
                    OrderItem.createNewOrderItemFrom(
                        it,
                        currentTime,
                        poId,
                    )
                }.toSet(),

                poNumber = persistedPurchaseOrder.poNumber,
                type = persistedPurchaseOrder.type,
                yearWeek = persistedPurchaseOrder.yearWeek,
                supplierId = persistedPurchaseOrder.supplierId,
                supplierCode = persistedPurchaseOrder.supplierCode,
                dcCode = persistedPurchaseOrder.dcCode,
                shippingMethod = persistedPurchaseOrder.shippingMethod,
                shippingAddress = persistedPurchaseOrder.shippingAddress,
                expectedStartTime = persistedPurchaseOrder.expectedStartTime,
                expectedEndTime = persistedPurchaseOrder.expectedEndTime,
                emergencyReasonUuid = persistedPurchaseOrder.emergencyReasonUuid,
                totalPrice = persistedPurchaseOrder.totalPrice,
                comment = persistedPurchaseOrder.comment,
                isSynced = false,
                deliveryDateChangeReasonId = null,
            )
        }

        @Suppress("LongParameterList")
        fun createPurchaseOrder(
            poId: UUID,
            poNumber: String,
            yearWeek: YearWeek,
            userId: UUID,
            userEmail: String,
            supplierId: UUID,
            supplierCode: String,
            dcCode: String,
            shippingMethod: ShipMethodEnum,
            shippingAddress: ShippingAddress,
            expectedStartTime: LocalDateTime,
            expectedEndTime: LocalDateTime,
            orderItems: Set<OrderItem>,
            emergencyReasonUuid: UUID,
            totalPrice: Money,
            comment: String?,
            currentTime: LocalDateTime,
            origin: Origin,
        ): PurchaseOrder =
            PurchaseOrder(
                status = INITIATED,
                type = EMERGENCY,
                version = 1,
                sendTime = null,
                id = poId,
                poNumber = poNumber,
                yearWeek = yearWeek,
                userId = userId,
                userEmail = userEmail,
                supplierId = supplierId,
                supplierCode = supplierCode,
                dcCode = dcCode,
                shippingMethod = shippingMethod,
                shippingAddress = shippingAddress,
                expectedStartTime = expectedStartTime,
                expectedEndTime = expectedEndTime,
                orderItems = orderItems,
                emergencyReasonUuid = emergencyReasonUuid,
                totalPrice = totalPrice,
                comment = comment,
                createdAt = currentTime,
                updatedAt = currentTime,
                isSynced = false,
                deliveryDateChangeReasonId = null,
                origin = origin,
            )
    }
}

@Embeddable
data class ShippingAddress(
    val locationName: String,
    val streetAddress: String,
    val city: String,
    val region: String,
    val postalCode: String,
    val countryCode: String,
)

enum class POType {
    STANDARD,
    EMERGENCY,
    PREORDER
}

enum class PurchaseOrderStatus {
    INITIATED,
    APPROVED,
    REJECTED,
    DELETED
}

enum class Origin {
    OTHER,
    MANUAL,
    PLANNED,
}
