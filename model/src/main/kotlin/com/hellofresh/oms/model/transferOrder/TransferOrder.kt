package com.hellofresh.oms.model.transferOrder

import jakarta.persistence.CascadeType
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import jakarta.persistence.FetchType
import jakarta.persistence.Id
import jakarta.persistence.OneToMany
import jakarta.persistence.Table
import java.time.LocalDateTime
import java.util.UUID

@Entity
@Table(name = "transfer_order")
data class TransferOrder(
    @Id
    val id: UUID,
    
    @Column(name = "transfer_order_number")
    val transferOrderNumber: String,
    
    @Column(name = "source_dc_code")
    val sourceDcCode: String,
    
    @Column(name = "destination_dc_code")
    val destinationDcCode: String,
    
    @Enumerated(EnumType.STRING)
    val status: TransferOrderStatus,
    
    @Column(name = "transfer_type")
    @Enumerated(EnumType.STRING)
    val transferType: TransferOrderType,
    
    @Column(name = "requested_by")
    val requestedBy: String?,
    
    @Column(name = "requested_at")
    val requestedAt: LocalDateTime?,
    
    @Column(name = "approved_by")
    val approvedBy: String?,
    
    @Column(name = "approved_at")
    val approvedAt: LocalDateTime?,
    
    @Column(name = "shipped_at")
    val shippedAt: LocalDateTime?,
    
    @Column(name = "received_at")
    val receivedAt: LocalDateTime?,
    
    val comments: String?,
    
    @Column(name = "created_at")
    val createdAt: LocalDateTime,
    
    @Column(name = "updated_at")
    val updatedAt: LocalDateTime,
    
    @OneToMany(
        cascade = [CascadeType.ALL],
        fetch = FetchType.LAZY,
        mappedBy = "transferOrderId"
    )
    val items: List<TransferOrderItem> = emptyList()
)

enum class TransferOrderStatus {
    REQUESTED,
    APPROVED,
    REJECTED,
    SHIPPED,
    RECEIVED,
    CANCELLED
}

enum class TransferOrderType {
    REGULAR,
    EMERGENCY,
    RETURN
}
