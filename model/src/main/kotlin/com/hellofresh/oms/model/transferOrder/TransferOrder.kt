package com.hellofresh.oms.model.transferOrder

import jakarta.persistence.CascadeType
import jakarta.persistence.Column
import jakarta.persistence.Embedded
import jakarta.persistence.Entity
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import jakarta.persistence.FetchType
import jakarta.persistence.Id
import jakarta.persistence.OneToMany
import jakarta.persistence.Table
import java.time.LocalDateTime
import java.util.UUID

@Entity
@Table(name = "transfer_order")
data class TransferOrder(
    @Id
    val id: UUID,

    @Column(name = "transfer_order_number")
    val transferOrderNumber: String,

    @Column(name = "source_dc_code")
    val sourceDcCode: String,

    @Column(name = "destination_dc_code")
    val destinationDcCode: String,

    @Column(name = "source_dc_name")
    val sourceDcName: String?,

    @Column(name = "creator_email")
    val creatorEmail: String?,

    @Column(name = "reason_text")
    val reasonText: String?,

    @Enumerated(EnumType.STRING)
    val status: TransferOrderStatus,

    @Column(name = "production_week_year")
    val productionWeekYear: Int?,

    @Column(name = "production_week_week")
    val productionWeekWeek: Int?,

    @Column(name = "region_code")
    val regionCode: String?,

    @Column(name = "market_code")
    val marketCode: String?,

    @Column(name = "shipping_method")
    val shippingMethod: String?,

    val version: Int?,

    @Embedded
    val totalPrice: TransferOrderPrice?,

    @Embedded
    val shippingAddress: TransferOrderShippingAddress?,

    @Column(name = "create_time")
    val createTime: LocalDateTime?,

    @Column(name = "update_time")
    val updateTime: LocalDateTime?,

    @Column(name = "pickup_start_time")
    val pickupStartTime: LocalDateTime?,

    @Column(name = "pickup_end_time")
    val pickupEndTime: LocalDateTime?,

    @Column(name = "delivery_start_time")
    val deliveryStartTime: LocalDateTime?,

    @Column(name = "delivery_end_time")
    val deliveryEndTime: LocalDateTime?,

    @Column(name = "created_at")
    val createdAt: LocalDateTime,

    @Column(name = "updated_at")
    val updatedAt: LocalDateTime,

    @OneToMany(
        cascade = [CascadeType.ALL],
        fetch = FetchType.LAZY,
        mappedBy = "transferOrderId"
    )
    val items: List<TransferOrderItem> = emptyList()
)

@jakarta.persistence.Embeddable
data class TransferOrderPrice(
    @Column(name = "total_price_currency")
    val currency: String?,

    @Column(name = "total_price_units")
    val units: String?,

    @Column(name = "total_price_nanos")
    val nanos: Long?
)

@jakarta.persistence.Embeddable
data class TransferOrderShippingAddress(
    @Column(name = "shipping_region_code")
    val regionCode: String?,

    @Column(name = "shipping_postal_code")
    val postalCode: String?,

    @Column(name = "shipping_administrative_area")
    val administrativeArea: String?,

    @Column(name = "shipping_locality")
    val locality: String?,

    @Column(name = "shipping_address_lines")
    val addressLines: Array<String>?,

    @Column(name = "shipping_organization")
    val organization: String?
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as TransferOrderShippingAddress

        if (regionCode != other.regionCode) return false
        if (postalCode != other.postalCode) return false
        if (administrativeArea != other.administrativeArea) return false
        if (locality != other.locality) return false
        if (addressLines != null) {
            if (other.addressLines == null) return false
            if (!addressLines.contentEquals(other.addressLines)) return false
        } else if (other.addressLines != null) return false
        if (organization != other.organization) return false

        return true
    }

    override fun hashCode(): Int {
        var result = regionCode?.hashCode() ?: 0
        result = 31 * result + (postalCode?.hashCode() ?: 0)
        result = 31 * result + (administrativeArea?.hashCode() ?: 0)
        result = 31 * result + (locality?.hashCode() ?: 0)
        result = 31 * result + (addressLines?.contentHashCode() ?: 0)
        result = 31 * result + (organization?.hashCode() ?: 0)
        return result
    }
}

enum class TransferOrderStatus {
    STATE_CANCELLED,
    STATE_REQUESTED,
    STATE_APPROVED,
    STATE_REJECTED,
    STATE_SHIPPED,
    STATE_RECEIVED
}
