-- Migration: V20250624_1400__add_transfer_order_tables.sql (Environment: common)
CREATE TABLE IF NOT EXISTS transfer_order (
    id UUID PRIMARY KEY,
    source_dc_code TEXT NOT NULL,
    destination_dc_code TEXT NOT NULL,
    creator_email TEXT,
    reason_text TEXT,
    status TEXT NOT NULL,
    production_week_year INTEGER,
    production_week_week INTEGER,
    create_time TIMESTAMP WITH TIME ZONE,
    update_time TIMESTAMP WITH TIME ZONE,
    pickup_start_time TIMESTAMP WITH TIME ZONE,
    pickup_end_time TIMESTAMP WITH TIME ZONE,
    delivery_start_time TIMESTAMP WITH TIME ZONE,
    delivery_end_time TIMESTAMP WITH TIME ZONE,
    transfer_order_number TEXT,
    region_code TEXT,
    source_dc_name TEXT,
    shipping_method TEXT,
    market_code TEXT,

    total_price_currency_code TEXT,
    total_price_units TEXT,
    total_price_nanos BIGINT,

    shipping_region_code TEXT,
    shipping_postal_code TEXT,
    shipping_administrative_area TEXT,
    shipping_locality TEXT,
    shipping_address_lines TEXT[],
    shipping_organization TEXT,
    shipping_method_from_shipping TEXT,

    version INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS transfer_order_item (
    id UUID PRIMARY KEY,
    transfer_order_id UUID NOT NULL REFERENCES transfer_order(id) ON DELETE CASCADE,
    csku_code TEXT NOT NULL,
    csku_name TEXT,
    sku_id UUID,
    supplier_id UUID,
    supplier_code TEXT,
    order_size INTEGER,
    inventory_type TEXT,
    price_currency_code TEXT,
    price_nanos BIGINT,
    total_price_currency_code TEXT,
    total_price_units TEXT,
    total_price_nanos BIGINT,
    case_packaging_size_value TEXT,
    case_packaging_unit TEXT,
    quantity_value TEXT,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_transfer_order_item_transfer_order_id ON transfer_order_item (transfer_order_id);
CREATE INDEX idx_transfer_order_source_dc ON transfer_order (source_dc_code);
CREATE INDEX idx_transfer_order_destination_dc ON transfer_order (destination_dc_code);
CREATE INDEX idx_transfer_order_status ON transfer_order (status);
CREATE INDEX idx_transfer_order_number ON transfer_order (transfer_order_number);
