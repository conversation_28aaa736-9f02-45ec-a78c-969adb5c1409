-- Migration: V20250624_1400__add_transfer_order_tables.sql (Environment: common)
CREATE TABLE IF NOT EXISTS transfer_order (
    id UUID PRIMARY KEY,
    transfer_order_number TEXT NOT NULL UNIQUE,
    source_dc_code TEXT NOT NULL,
    destination_dc_code TEXT NOT NULL,
    status TEXT NOT NULL,
    transfer_type TEXT NOT NULL,
    requested_by TEXT,
    requested_at TIMESTAMP WITH TIME ZONE,
    approved_by TEXT,
    approved_at TIMESTAMP WITH TIME ZONE,
    shipped_at TIMESTAMP WITH TIME ZONE,
    received_at TIMESTAMP WITH TIME ZONE,
    comments TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS transfer_order_item (
    id UUID PRIMARY KEY,
    transfer_order_id UUID NOT NULL REFERENCES transfer_order(id) ON DELETE CASCADE,
    sku_code TEXT NOT NULL,
    sku_id UUID,
    requested_quantity NUMERIC NOT NULL,
    approved_quantity NUMERIC,
    shipped_quantity NUMERIC,
    received_quantity NUMERIC,
    unit_of_measure TEXT,
    case_size NUMERIC,
    comments TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_transfer_order_item_transfer_order_id ON transfer_order_item (transfer_order_id);
CREATE INDEX idx_transfer_order_source_dc ON transfer_order (source_dc_code);
CREATE INDEX idx_transfer_order_destination_dc ON transfer_order (destination_dc_code);
CREATE INDEX idx_transfer_order_status ON transfer_order (status);
