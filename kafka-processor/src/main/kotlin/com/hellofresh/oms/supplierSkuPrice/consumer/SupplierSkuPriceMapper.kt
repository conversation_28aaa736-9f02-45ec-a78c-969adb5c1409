package com.hellofresh.oms.supplierSkuPrice.consumer

import com.hellofresh.oms.model.Permyriad
import com.hellofresh.oms.model.SupplierSkuPrice
import com.hellofresh.planning.suppliersku.pricing.pricing as AvroSupplierSkuPrice
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

fun AvroSupplierSkuPrice.mapToSupplierSkuPrice() =
    SupplierSkuPrice(
        uuid = id,
        supplierSkuId = supplierSkuId,
        dcCodes = distributionCenter,
        startDate = toLocalDateTime(startDate),
        endDate = toLocalDateTime(endDate),
        enabled = enabled,
        priceType = priceType,
        market = market,
        currency = currency,
        pricePermyriad = Permyriad(price),
        // We pass buffer percent directly into Permyriad constructor nice
        // percentage value is multiply by 100 by producer
        // see pricing schema for more details.
        bufferPermyriad = Permyriad(bufferPercent),
        caseSize = mapCaseSize(unitsPerCase),
        casePrice = Permyriad(casePrice),
        createdAt = toLocalDateTime(createdAt),
        updatedAt = toLocalDateTime(updatedAt),
    )

private fun mapCaseSize(caseSize: Int) =
    if (caseSize == 0) {
        null
    } else {
        caseSize
    }

private fun toLocalDateTime(dateTime: String) =
    LocalDateTime.parse(dateTime, DateTimeFormatter.ISO_DATE_TIME)
