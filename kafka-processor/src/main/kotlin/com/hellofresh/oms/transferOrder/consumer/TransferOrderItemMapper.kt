package com.hellofresh.oms.transferOrder.consumer

import com.hellofresh.oms.model.transferOrder.TransferOrderItem as TransferOrderItemEntity
import com.hellofresh.oms.model.transferOrder.TransferOrderItemCasePackaging
import com.hellofresh.proto.stream.transferOrder.v1.CasePackaging
import com.hellofresh.proto.stream.transferOrder.v1.TransferOrderItem as AvroTransferOrderItem
import java.time.LocalDateTime
import java.util.UUID

fun AvroTransferOrderItem.toTransferOrderItem(transferOrderId: UUID): TransferOrderItemEntity {
    val packagingType = when {
        this.hasCasePackaging() -> "case"
        this.hasUnitPackaging() -> "unit"
        else -> "unit"
    }

    return TransferOrderItemEntity(
        id = UUID.fromString(this.id.toString()),
        transferOrderId = transferOrderId,
        cskuCode = this.cskuCode.toString(),
        cskuName = this.cskuName.toString(),
        supplierId = UUID.fromString(this.supplierId.toString()),
        supplierCode = this.supplierCode.toString(),
        orderSize = this.orderSize,
        inventoryType = this.inventoryType.toString(),
        packagingType = packagingType,
        price = this.price.toTransferOrderItemPrice(),
        totalPrice = this.totalPrice.toTransferOrderItemTotalPrice(),
        quantity = this.quantity.toTransferOrderItemQuantity(),

        // Optional fields
        originalPoNumber = this.originalPoNumber,
        lotNumber = this.lotNumber,
        lotExpirationTime = this.lotExpirationTime?.let { parseTimestamp(it) },
        skuId = this.skuId?.let { UUID.fromString(it) },
        licensePlateNumber = this.licensePlateNumber,
        casePackaging = if (this.hasCasePackaging()) this.casePackaging.toTransferOrderItemCasePackaging() else null,

        createdAt = LocalDateTime.now(),
        updatedAt = LocalDateTime.now(),
    )
}

fun CasePackaging.toTransferOrderItemCasePackaging(): TransferOrderItemCasePackaging = TransferOrderItemCasePackaging(
    sizeValue = this.size?.value,
    unit = this.unit?.toString(),
    casePerPallet = this.casePerPallet,
)
