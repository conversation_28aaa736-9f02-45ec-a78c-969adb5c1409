package com.hellofresh.oms.transferOrder.consumer

import com.hellofresh.oms.model.transferOrder.TransferOrderChangeReason
import com.hellofresh.oms.model.transferOrder.TransferOrderItemsChangeReason
import com.hellofresh.proto.stream.transferOrder.v1.DeliveryChangeReason
import com.hellofresh.proto.stream.transferOrder.v1.OrderItemsChangeReason

fun DeliveryChangeReason.toTransferOrderChangeReason(): TransferOrderChangeReason? {
    return if (this.key.isNotBlank() || this.value.isNotBlank()) {
        TransferOrderChangeReason(
            key = this.key.takeIf { it.isNotBlank() },
            value = this.value.takeIf { it.isNotBlank() },
        )
    } else {
        null
    }
}

fun OrderItemsChangeReason.toTransferOrderItemsChangeReason(): TransferOrderItemsChangeReason? {
    return if (this.key.isNotBlank() || this.value.isNotBlank()) {
        TransferOrderItemsChangeReason(
            key = this.key.takeIf { it.isNotBlank() },
            value = this.value.takeIf { it.isNotBlank() },
        )
    } else {
        null
    }
}
