package com.hellofresh.oms.transferOrder.consumer

import com.google.type.PostalAddress
import com.hellofresh.oms.model.transferOrder.AddressInfo
import com.hellofresh.oms.model.transferOrder.ShippingInfo
import com.hellofresh.proto.stream.transferOrder.v1.Shipping

fun Shipping.toShippingInfo(): ShippingInfo = ShippingInfo(
    address = this.address.toAddressInfo(),
    method = this.method,
    notes = this.notes,
)

fun PostalAddress.toAddressInfo(): AddressInfo = AddressInfo(
    postalCode = this.postalCode,
    administrativeArea = this.administrativeArea,
    locality = this.locality,
    addressLines = this.addressLinesList,
    organization = this.organization,
    recipients = this.recipientsList.takeIf { it.isNotEmpty() },
    revision = this.revision.takeIf { it != 0 },
    regionCode = this.regionCode,
)
