package com.hellofresh.oms.transferOrder.consumer

import com.google.type.Decimal
import com.google.type.Money
import com.hellofresh.oms.model.transferOrder.TransferOrderItemPrice
import com.hellofresh.oms.model.transferOrder.TransferOrderItemQuantity
import com.hellofresh.oms.model.transferOrder.TransferOrderItemTotalPrice
import com.hellofresh.oms.model.transferOrder.TransferOrderPrice

fun Money.toTransferOrderPrice(): TransferOrderPrice = TransferOrderPrice(
    currencyCode = this.currencyCode,
    units = this.units,
    nanos = this.nanos,
)

fun Money.toTransferOrderItemPrice(): TransferOrderItemPrice = TransferOrderItemPrice(
    currencyCode = this.currencyCode,
    units = this.units,
    nanos = this.nanos,
)

fun Money.toTransferOrderItemTotalPrice(): TransferOrderItemTotalPrice = TransferOrderItemTotalPrice(
    currencyCode = this.currencyCode,
    units = this.units,
    nanos = this.nanos,
)

fun Decimal.toTransferOrderItemQuantity(): TransferOrderItemQuantity = TransferOrderItemQuantity(
    value = this.value.toBigDecimal(),
)
