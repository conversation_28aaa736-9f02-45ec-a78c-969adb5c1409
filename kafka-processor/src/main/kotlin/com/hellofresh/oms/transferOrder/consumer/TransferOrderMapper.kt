package com.hellofresh.oms.transferOrder.consumer

import com.hellofresh.oms.model.transferOrder.TransferOrder as TransferOrderEntity
import com.hellofresh.oms.model.transferOrder.TransferOrderItem as TransferOrderItemEntity
import com.hellofresh.oms.model.transferOrder.TransferOrderItemCasePackaging
import com.hellofresh.oms.model.transferOrder.TransferOrderItemPrice
import com.hellofresh.oms.model.transferOrder.TransferOrderItemQuantity
import com.hellofresh.oms.model.transferOrder.TransferOrderItemTotalPrice
import com.hellofresh.oms.model.transferOrder.TransferOrderPrice
import com.hellofresh.oms.model.transferOrder.TransferOrderShippingAddress
import com.hellofresh.oms.model.transferOrder.TransferOrderStatus
import com.hellofresh.proto.stream.transferOrder.v1.TransferOrder as AvroTransferOrder
import com.hellofresh.proto.stream.transferOrder.v1.TransferOrderItem as AvroTransferOrderItem
import java.time.Instant
import java.time.LocalDateTime
import java.time.ZoneOffset
import java.util.UUID

/**
 * Maps Avro TransferOrder to TransferOrder entity.
 * This mapper handles the public.transfer-order.v1 schema structure.
 */
fun AvroTransferOrder.toTransferOrder(): TransferOrderEntity {
    val transferOrderId = UUID.fromString(this.id.toString())
    val items = this.items?.map { item ->
        item.toTransferOrderItem(transferOrderId)
    } ?: emptyList()

    return TransferOrderEntity(
        id = transferOrderId,
        sourceDcCode = this.sourceDcCode.toString(),
        destinationDcCode = this.destinationDcCode.toString(),
        creatorEmail = this.creatorEmail.toString(),
        reasonText = this.reasonText.toString(),
        status = this.status.toString().toTransferOrderStatus(),
        productionWeekYear = this.productionWeek.year,
        productionWeekWeek = this.productionWeek.week,
        transferOrderNumber = this.transferOrderNumber.toString(),
        sourceDcName = this.sourceDcName.toString(),
        shippingMethod = this.shippingMethod.toString(),
        marketCode = this.marketCode.toString(),
        version = this.version,
        totalPrice = this.totalPrice.toTransferOrderPrice(),
        shippingAddress = this.shipping.address.toTransferOrderShippingAddress(),
        shipping = this.shipping.toTransferOrderShipping(),
        createTime = parseTimestamp(this.createTime)!!,
        updateTime = parseTimestamp(this.updateTime)!!,
        pickupStartTime = parseTimestamp(this.pickupStartTime)!!,
        pickupEndTime = parseTimestamp(this.pickupEndTime)!!,
        deliveryStartTime = parseTimestamp(this.deliveryStartTime)!!,
        deliveryEndTime = parseTimestamp(this.deliveryEndTime)!!,
        sentTime = parseTimestamp(this.sentTime)!!,

        // Optional fields
        comments = this.comments,
        deliveryChangeReason = this.deliveryChangeReason?.toTransferOrderChangeReason(),
        orderItemsChangeReason = this.orderItemsChangeReason?.toTransferOrderItemsChangeReason(),
        regionCode = this.regionCode?.toString(),

        createdAt = LocalDateTime.now(),
        updatedAt = LocalDateTime.now(),
        items = items
    )
}

fun AvroTransferOrderItem.toTransferOrderItem(transferOrderId: UUID): TransferOrderItemEntity {
    return TransferOrderItemEntity(
        id = UUID.fromString(this.id.toString()),
        transferOrderId = transferOrderId,
        cskuCode = this.cskuCode.toString(),
        cskuName = this.cskuName?.toString(),
        skuId = this.skuId?.let { UUID.fromString(it.toString()) },
        supplierId = this.supplierId?.let { UUID.fromString(it.toString()) },
        supplierCode = this.supplierCode?.toString(),
        orderSize = this.orderSize,
        inventoryType = this.inventoryType?.toString(),
        price = this.price?.toTransferOrderItemPrice(),
        totalPrice = this.totalPrice?.toTransferOrderItemTotalPrice(),
        casePackaging = this.casePackaging?.toTransferOrderItemCasePackaging(),
        quantity = this.quantity?.toTransferOrderItemQuantity(),
        createdAt = LocalDateTime.now(),
        updatedAt = LocalDateTime.now()
    )
}

// Helper functions for mapping embedded objects
fun com.hellofresh.proto.stream.transferOrder.v1.Money.toTransferOrderPrice(): TransferOrderPrice {
    return TransferOrderPrice(
        currency = this.currencyCode?.toString(),
        units = this.units?.toString(),
        nanos = this.nanos?.toLong()
    )
}

fun com.hellofresh.proto.stream.transferOrder.v1.Money.toTransferOrderItemPrice(): TransferOrderItemPrice {
    return TransferOrderItemPrice(
        currencyCode = this.currencyCode?.toString(),
        nanos = this.nanos?.toLong()
    )
}

fun com.hellofresh.proto.stream.transferOrder.v1.Money.toTransferOrderItemTotalPrice(): TransferOrderItemTotalPrice {
    return TransferOrderItemTotalPrice(
        currencyCode = this.currencyCode?.toString(),
        units = this.units?.toString(),
        nanos = this.nanos?.toLong()
    )
}

fun com.hellofresh.proto.stream.transferOrder.v1.Address.toTransferOrderShippingAddress(): TransferOrderShippingAddress {
    return TransferOrderShippingAddress(
        regionCode = this.regionCode?.toString(),
        postalCode = this.postalCode?.toString(),
        administrativeArea = this.administrativeArea?.toString(),
        locality = this.locality?.toString(),
        addressLines = this.addressLines?.map { it.toString() }?.toTypedArray(),
        organization = this.organization?.toString()
    )
}

fun com.hellofresh.proto.stream.transferOrder.v1.CasePackaging.toTransferOrderItemCasePackaging(): TransferOrderItemCasePackaging {
    return TransferOrderItemCasePackaging(
        sizeValue = this.size?.value?.toString(),
        unit = this.unit?.toString()
    )
}

fun com.hellofresh.proto.stream.transferOrder.v1.Quantity.toTransferOrderItemQuantity(): TransferOrderItemQuantity {
    return TransferOrderItemQuantity(
        value = this.value?.toString()
    )
}

private fun String.toTransferOrderStatus(): TransferOrderStatus {
    return when (this.uppercase()) {
        "STATE_CANCELLED" -> TransferOrderStatus.STATE_CANCELLED
        "STATE_REQUESTED" -> TransferOrderStatus.STATE_REQUESTED
        "STATE_APPROVED" -> TransferOrderStatus.STATE_APPROVED
        "STATE_REJECTED" -> TransferOrderStatus.STATE_REJECTED
        "STATE_SHIPPED" -> TransferOrderStatus.STATE_SHIPPED
        "STATE_RECEIVED" -> TransferOrderStatus.STATE_RECEIVED
        else -> TransferOrderStatus.STATE_REQUESTED
    }
}

private fun parseTimestamp(value: Any?): LocalDateTime? {
    return when (value) {
        is String -> {
            try {
                Instant.parse(value).atOffset(ZoneOffset.UTC).toLocalDateTime()
            } catch (e: Exception) {
                null
            }
        }
        is Long -> Instant.ofEpochMilli(value).atOffset(ZoneOffset.UTC).toLocalDateTime()
        else -> null
    }
}
