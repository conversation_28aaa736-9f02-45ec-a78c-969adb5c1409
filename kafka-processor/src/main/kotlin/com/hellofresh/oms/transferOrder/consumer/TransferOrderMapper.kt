package com.hellofresh.oms.transferOrder.consumer

import com.google.protobuf.Timestamp
import com.google.type.Decimal
import com.google.type.Money
import com.google.type.PostalAddress
import com.hellofresh.oms.model.YearWeek
import com.hellofresh.oms.model.transferOrder.AddressInfo
import com.hellofresh.oms.model.transferOrder.ShippingInfo
import com.hellofresh.oms.model.transferOrder.TransferOrder as TransferOrderEntity
import com.hellofresh.oms.model.transferOrder.TransferOrderItem as TransferOrderItemEntity
import com.hellofresh.oms.model.transferOrder.TransferOrderChangeReason
import com.hellofresh.oms.model.transferOrder.TransferOrderItemCasePackaging
import com.hellofresh.oms.model.transferOrder.TransferOrderItemPrice
import com.hellofresh.oms.model.transferOrder.TransferOrderItemQuantity
import com.hellofresh.oms.model.transferOrder.TransferOrderItemTotalPrice
import com.hellofresh.oms.model.transferOrder.TransferOrderItemsChangeReason
import com.hellofresh.oms.model.transferOrder.TransferOrderPrice
import com.hellofresh.proto.stream.transferOrder.v1.CasePackaging
import com.hellofresh.oms.model.transferOrder.TransferOrderStatus
import com.hellofresh.proto.stream.transferOrder.v1.DeliveryChangeReason
import com.hellofresh.proto.stream.transferOrder.v1.OrderItemsChangeReason
import com.hellofresh.proto.stream.transferOrder.v1.Shipping
import com.hellofresh.proto.stream.transferOrder.v1.TransferOrder as AvroTransferOrder
import com.hellofresh.proto.stream.transferOrder.v1.TransferOrderItem as AvroTransferOrderItem
import java.math.BigDecimal
import java.time.Instant
import java.time.LocalDateTime
import java.time.ZoneOffset
import java.util.UUID

fun AvroTransferOrder.toTransferOrder(): TransferOrderEntity {
    val transferOrderId = UUID.fromString(this.id.toString())
    val items = this.itemsList?.map { item ->
        item.toTransferOrderItem(transferOrderId)
    } ?: emptyList()

    return TransferOrderEntity(
        id = transferOrderId,
        sourceDcCode = this.sourceDcCode.toString(),
        destinationDcCode = this.destinationDcCode.toString(),
        creatorEmail = this.creatorEmail.toString(),
        reasonText = this.reasonText.toString(),
        status = TransferOrderStatus.valueOf(this.status.toString()),
        yearWeek = YearWeek(this.productionWeek.year, this.productionWeek.week),
        transferOrderNumber = this.transferOrderNumber.toString(),
        sourceDcName = this.sourceDcName.toString(),
        marketCode = this.marketCode.toString(),
        version = this.version,
        totalPrice = this.totalPrice.toTransferOrderPrice(),
        shipping = this.shipping.toShippingInfo(),
        createTime = parseTimestamp(this.createTime),
        updateTime = parseTimestamp(this.updateTime),
        pickupStartTime = parseTimestamp(this.pickupStartTime),
        pickupEndTime = parseTimestamp(this.pickupEndTime),
        deliveryStartTime = parseTimestamp(this.deliveryStartTime),
        deliveryEndTime = parseTimestamp(this.deliveryEndTime),
        sentTime = parseTimestamp(this.sentTime),
        comments = this.comments.takeIf { it.isNotBlank() },
        deliveryChangeReason = this.deliveryChangeReason?.toTransferOrderChangeReason(),
        orderItemsChangeReason = this.orderItemsChangeReason?.toTransferOrderItemsChangeReason(),
        regionCode = this.regionCode,

        createdAt = LocalDateTime.now(),
        updatedAt = LocalDateTime.now(),
        items = items,
    )
}

fun AvroTransferOrderItem.toTransferOrderItem(transferOrderId: UUID): TransferOrderItemEntity {
    val packagingType = when {
        this.hasCasePackaging() -> "case"
        this.hasUnitPackaging() -> "unit"
        else -> "unit"
    }

    return TransferOrderItemEntity(
        id = UUID.fromString(this.id.toString()),
        transferOrderId = transferOrderId,
        cskuCode = this.cskuCode.toString(),
        cskuName = this.cskuName.toString(),
        supplierId = UUID.fromString(this.supplierId.toString()),
        supplierCode = this.supplierCode.toString(),
        orderSize = this.orderSize,
        inventoryType = this.inventoryType.toString(),
        packagingType = packagingType,
        price = this.price.toTransferOrderItemPrice(),
        totalPrice = this.totalPrice.toTransferOrderItemTotalPrice(),
        quantity = this.quantity.toTransferOrderItemQuantity(),

        // Optional fields
        originalPoNumber = this.originalPoNumber,
        lotNumber = this.lotNumber,
        lotExpirationTime = this.lotExpirationTime?.let { parseTimestamp(it) },
        skuId = this.skuId?.let { UUID.fromString(it) },
        licensePlateNumber = this.licensePlateNumber,
        casePackaging = if (this.hasCasePackaging()) this.casePackaging.toTransferOrderItemCasePackaging() else null,

        createdAt = LocalDateTime.now(),
        updatedAt = LocalDateTime.now(),
    )
}

fun Money.toTransferOrderPrice(): TransferOrderPrice {
    return TransferOrderPrice(
        currencyCode = this.currencyCode,
        units = this.units,
        nanos = this.nanos,
    )
}

fun Money.toTransferOrderItemPrice(): TransferOrderItemPrice {
    return TransferOrderItemPrice(
        currencyCode = this.currencyCode,
        units = this.units,
        nanos = this.nanos,
    )
}

fun Money.toTransferOrderItemTotalPrice(): TransferOrderItemTotalPrice {
    return TransferOrderItemTotalPrice(
        currencyCode = this.currencyCode,
        units = this.units,
        nanos = this.nanos,
    )
}

fun DeliveryChangeReason.toTransferOrderChangeReason(): TransferOrderChangeReason? {
    return if (this.key.isNotBlank() || this.value.isNotBlank()) {
        TransferOrderChangeReason(
            key = this.key.takeIf { it.isNotBlank() },
            value = this.value.takeIf { it.isNotBlank() },
        )
    } else {
        null
    }
}

fun OrderItemsChangeReason.toTransferOrderItemsChangeReason(): TransferOrderItemsChangeReason? {
    return if (this.key.isNotBlank() || this.value.isNotBlank()) {
        TransferOrderItemsChangeReason(
            key = this.key.takeIf { it.isNotBlank() },
            value = this.value.takeIf { it.isNotBlank() },
        )
    } else {
        null
    }
}

fun Shipping.toShippingInfo(): ShippingInfo {
    return ShippingInfo(
        address = this.address.toAddressInfo(),
        method = this.method,
        notes = this.notes,
    )
}

fun PostalAddress.toAddressInfo(): AddressInfo {
    return AddressInfo(
        postalCode = this.postalCode,
        administrativeArea = this.administrativeArea,
        locality = this.locality,
        addressLines = this.addressLinesList,
        organization = this.organization,
        recipients = this.recipientsList.takeIf { it.isNotEmpty() },
        revision = this.revision.takeIf { it != 0 },
        regionCode = this.regionCode,
    )
}

fun CasePackaging.toTransferOrderItemCasePackaging(): TransferOrderItemCasePackaging {
    return TransferOrderItemCasePackaging(
        sizeValue = this.size?.value,
        unit = this.unit?.toString(),
        casePerPallet = this.casePerPallet,
    )
}

fun Decimal.toTransferOrderItemQuantity(): TransferOrderItemQuantity {
    return TransferOrderItemQuantity(
        value = this.value.toBigDecimal(),
    )
}

private fun parseTimestamp(timestamp: Timestamp): LocalDateTime {
    return Instant.ofEpochSecond(timestamp.seconds, timestamp.nanos.toLong())
        .atOffset(ZoneOffset.UTC)
        .toLocalDateTime()
}
