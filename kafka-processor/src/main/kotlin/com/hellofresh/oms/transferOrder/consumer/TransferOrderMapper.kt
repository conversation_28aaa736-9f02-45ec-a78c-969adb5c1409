package com.hellofresh.oms.transferOrder.consumer

import com.google.protobuf.Timestamp
import com.hellofresh.oms.model.YearWeek
import com.hellofresh.oms.model.transferOrder.AddressInfo
import com.hellofresh.oms.model.transferOrder.ShippingInfo
import com.hellofresh.oms.model.transferOrder.TransferOrder as TransferOrderEntity
import com.hellofresh.oms.model.transferOrder.TransferOrderChangeReason
import com.hellofresh.oms.model.transferOrder.TransferOrderItem as TransferOrderItemEntity
import com.hellofresh.oms.model.transferOrder.TransferOrderItemCasePackaging
import com.hellofresh.oms.model.transferOrder.TransferOrderItemPrice
import com.hellofresh.oms.model.transferOrder.TransferOrderItemQuantity
import com.hellofresh.oms.model.transferOrder.TransferOrderItemTotalPrice
import com.hellofresh.oms.model.transferOrder.TransferOrderItemsChangeReason
import com.hellofresh.oms.model.transferOrder.TransferOrderPrice
import com.hellofresh.oms.model.transferOrder.TransferOrderStatus
import com.hellofresh.proto.stream.transferOrder.v1.TransferOrder as AvroTransferOrder
import com.hellofresh.proto.stream.transferOrder.v1.TransferOrderItem as AvroTransferOrderItem
import java.time.Instant
import java.time.LocalDateTime
import java.time.ZoneOffset
import java.util.UUID

fun AvroTransferOrder.toTransferOrder(): TransferOrderEntity {
    val transferOrderId = UUID.fromString(this.id.toString())
    val items = this.itemsList?.map { item ->
        item.toTransferOrderItem(transferOrderId)
    } ?: emptyList()

    return TransferOrderEntity(
        id = transferOrderId,
        sourceDcCode = this.sourceDcCode.toString(),
        destinationDcCode = this.destinationDcCode.toString(),
        creatorEmail = this.creatorEmail.toString(),
        reasonText = this.reasonText.toString(),
        status = TransferOrderStatus.valueOf(this.status.toString()),
        yearWeek = YearWeek(this.productionWeek.year, this.productionWeek.week),
        transferOrderNumber = this.transferOrderNumber.toString(),
        sourceDcName = this.sourceDcName.toString(),
        marketCode = this.marketCode.toString(),
        version = this.version,
        totalPrice = TransferOrderPrice(
            currencyCode = this.totalPrice.currencyCode,
            units = this.totalPrice.units,
            nanos = this.totalPrice.nanos,
        ),
        shipping = createShippingInfo(this.shipping),
        createTime = parseTimestamp(this.createTime),
        updateTime = parseTimestamp(this.updateTime),
        pickupStartTime = parseTimestamp(this.pickupStartTime),
        pickupEndTime = parseTimestamp(this.pickupEndTime),
        deliveryStartTime = parseTimestamp(this.deliveryStartTime),
        deliveryEndTime = parseTimestamp(this.deliveryEndTime),
        sentTime = parseTimestamp(this.sentTime),
        comments = this.comments.takeIf { it.isNotBlank() },
        deliveryChangeReason = this.deliveryChangeReason?.let { reason ->
            if (reason.key.isNotBlank() || reason.value.isNotBlank()) {
                TransferOrderChangeReason(
                    key = reason.key.takeIf { it.isNotBlank() },
                    value = reason.value.takeIf { it.isNotBlank() },
                )
            } else {
                null
            }
        },
        orderItemsChangeReason = this.orderItemsChangeReason?.let { reason ->
            if (reason.key.isNotBlank() || reason.value.isNotBlank()) {
                TransferOrderItemsChangeReason(
                    key = reason.key.takeIf { it.isNotBlank() },
                    value = reason.value.takeIf { it.isNotBlank() },
                )
            } else {
                null
            }
        },
        regionCode = this.regionCode,

        createdAt = LocalDateTime.now(),
        updatedAt = LocalDateTime.now(),
        items = items,
    )
}

fun AvroTransferOrderItem.toTransferOrderItem(transferOrderId: UUID): TransferOrderItemEntity {
    val packagingType = when {
        this.hasCasePackaging() -> "case"
        this.hasUnitPackaging() -> "unit"
        else -> "unit"
    }

    return TransferOrderItemEntity(
        id = UUID.fromString(this.id.toString()),
        transferOrderId = transferOrderId,
        cskuCode = this.cskuCode.toString(),
        cskuName = this.cskuName.toString(),
        supplierId = UUID.fromString(this.supplierId.toString()),
        supplierCode = this.supplierCode.toString(),
        orderSize = this.orderSize,
        inventoryType = this.inventoryType.toString(),
        packagingType = packagingType,
        price = TransferOrderItemPrice(
            currencyCode = this.price.currencyCode,
            units = this.price.units,
            nanos = this.price.nanos,
        ),
        totalPrice = TransferOrderItemTotalPrice(
            currencyCode = this.totalPrice.currencyCode,
            units = this.totalPrice.units,
            nanos = this.totalPrice.nanos,
        ),
        quantity = TransferOrderItemQuantity(
            value = this.quantity.value.toBigDecimal(),
        ),

        originalPoNumber = this.originalPoNumber,
        lotNumber = this.lotNumber,
        lotExpirationTime = this.lotExpirationTime?.let { parseTimestamp(it) },
        skuId = this.skuId?.let { UUID.fromString(it) },
        licensePlateNumber = this.licensePlateNumber,
        casePackaging = if (this.hasCasePackaging()) {
            TransferOrderItemCasePackaging(
                sizeValue = this.casePackaging.size?.value,
                unit = this.casePackaging.unit?.toString(),
                casePerPallet = this.casePackaging.casePerPallet,
            )
        } else {
            null
        },

        createdAt = LocalDateTime.now(),
        updatedAt = LocalDateTime.now(),
    )
}

private fun createShippingInfo(shipping: com.hellofresh.proto.stream.transferOrder.v1.Shipping): ShippingInfo = ShippingInfo(
    address = AddressInfo(
        postalCode = shipping.address.postalCode,
        administrativeArea = shipping.address.administrativeArea,
        locality = shipping.address.locality,
        addressLines = shipping.address.addressLinesList,
        organization = shipping.address.organization,
        recipients = shipping.address.recipientsList.takeIf { it.isNotEmpty() },
        revision = shipping.address.revision.takeIf { it != 0 },
        regionCode = shipping.address.regionCode,
    ),
    method = shipping.method,
    notes = shipping.notes,
)

private fun parseTimestamp(timestamp: Timestamp): LocalDateTime = Instant.ofEpochSecond(
    timestamp.seconds,
    timestamp.nanos.toLong()
).atOffset(ZoneOffset.UTC).toLocalDateTime()
