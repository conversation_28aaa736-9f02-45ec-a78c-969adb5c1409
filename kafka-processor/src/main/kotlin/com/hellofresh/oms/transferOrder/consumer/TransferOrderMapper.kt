package com.hellofresh.oms.transferOrder.consumer

import com.hellofresh.oms.model.transferOrder.TransferOrder
import com.hellofresh.oms.model.transferOrder.TransferOrderItem
import com.hellofresh.oms.model.transferOrder.TransferOrderStatus
import com.hellofresh.oms.model.transferOrder.TransferOrderType
import org.apache.avro.generic.GenericRecord
import java.math.BigDecimal
import java.time.Instant
import java.time.LocalDateTime
import java.time.ZoneOffset
import java.util.UUID

/**
 * Maps Avro GenericRecord to TransferOrder entity.
 * This mapper assumes the Avro schema follows the public.transfer-order.v1 structure.
 */
fun GenericRecord.toTransferOrder(): TransferOrder {
    val transferOrderId = UUID.fromString(this.get("id").toString())
    val items = (this.get("items") as? List<*>)?.mapNotNull { item ->
        (item as? GenericRecord)?.toTransferOrderItem(transferOrderId)
    } ?: emptyList()
    
    return TransferOrder(
        id = transferOrderId,
        transferOrderNumber = this.get("transfer_order_number").toString(),
        sourceDcCode = this.get("source_dc_code").toString(),
        destinationDcCode = this.get("destination_dc_code").toString(),
        status = this.get("status").toString().toTransferOrderStatus(),
        transferType = this.get("transfer_type").toString().toTransferOrderType(),
        requestedBy = this.get("requested_by")?.toString(),
        requestedAt = this.get("requested_at")?.let { parseTimestamp(it) },
        approvedBy = this.get("approved_by")?.toString(),
        approvedAt = this.get("approved_at")?.let { parseTimestamp(it) },
        shippedAt = this.get("shipped_at")?.let { parseTimestamp(it) },
        receivedAt = this.get("received_at")?.let { parseTimestamp(it) },
        comments = this.get("comments")?.toString(),
        createdAt = parseTimestamp(this.get("created_at")) ?: LocalDateTime.now(),
        updatedAt = parseTimestamp(this.get("updated_at")) ?: LocalDateTime.now(),
        items = items
    )
}

fun GenericRecord.toTransferOrderItem(transferOrderId: UUID): TransferOrderItem {
    return TransferOrderItem(
        id = UUID.fromString(this.get("id").toString()),
        transferOrderId = transferOrderId,
        skuCode = this.get("sku_code").toString(),
        skuId = this.get("sku_id")?.let { UUID.fromString(it.toString()) },
        requestedQuantity = BigDecimal(this.get("requested_quantity").toString()),
        approvedQuantity = this.get("approved_quantity")?.let { BigDecimal(it.toString()) },
        shippedQuantity = this.get("shipped_quantity")?.let { BigDecimal(it.toString()) },
        receivedQuantity = this.get("received_quantity")?.let { BigDecimal(it.toString()) },
        unitOfMeasure = this.get("unit_of_measure")?.toString(),
        caseSize = this.get("case_size")?.let { BigDecimal(it.toString()) },
        comments = this.get("comments")?.toString(),
        createdAt = parseTimestamp(this.get("created_at")) ?: LocalDateTime.now(),
        updatedAt = parseTimestamp(this.get("updated_at")) ?: LocalDateTime.now()
    )
}

private fun String.toTransferOrderStatus(): TransferOrderStatus {
    return when (this.uppercase()) {
        "REQUESTED" -> TransferOrderStatus.REQUESTED
        "APPROVED" -> TransferOrderStatus.APPROVED
        "REJECTED" -> TransferOrderStatus.REJECTED
        "SHIPPED" -> TransferOrderStatus.SHIPPED
        "RECEIVED" -> TransferOrderStatus.RECEIVED
        "CANCELLED" -> TransferOrderStatus.CANCELLED
        else -> TransferOrderStatus.REQUESTED
    }
}

private fun String.toTransferOrderType(): TransferOrderType {
    return when (this.uppercase()) {
        "REGULAR" -> TransferOrderType.REGULAR
        "EMERGENCY" -> TransferOrderType.EMERGENCY
        "RETURN" -> TransferOrderType.RETURN
        else -> TransferOrderType.REGULAR
    }
}

private fun parseTimestamp(value: Any?): LocalDateTime? {
    return when (value) {
        is String -> {
            try {
                Instant.parse(value).atOffset(ZoneOffset.UTC).toLocalDateTime()
            } catch (e: Exception) {
                null
            }
        }
        is Long -> Instant.ofEpochMilli(value).atOffset(ZoneOffset.UTC).toLocalDateTime()
        else -> null
    }
}
