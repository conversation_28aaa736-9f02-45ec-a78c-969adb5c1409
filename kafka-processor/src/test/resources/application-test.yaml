spring:
  cloud:
    function:
      definition: processSupplier;processShipMethod;processSku;processSupplierSku;processSupplierSkuPrice;processPurchaseOrder;processDistributionCenter;processSupplierSkuPackaging;processGoodsReceivedNote;processAcknowledgement;processImtExportReceipt;processTransferOrder
    stream:
      kafka:
        binder:
          autoCreateTopics: false
          consumer-properties:
            spring.deserializer.value.delegate.class: com.hellofresh.oms.integrationTestUtils.serde.CustomAvroDeserializer
            schema.registry.url: mock://dummy-registry
  flyway:
    locations: "classpath:db/common"

management:
  tracing:
    enabled: false

tapioca:
  base-url: "http://localhost:${wiremock.server.port}"

auth-service:
  base-url: "http://localhost:${wiremock.server.port}"
  client-id: "client_id"
  client-secret: "client_secret"

wiremock:
  server:
    port: 0

resilience4j:
  retry:
    instances:
      approvePurchaseOrder:
        maxAttempts: 3
        waitDuration: 100ms
      rejectPurchaseOrder:
        maxAttempts: 3
        waitDuration: 100ms
