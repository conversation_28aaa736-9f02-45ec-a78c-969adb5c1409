package com.hellofresh.oms.transferOrder.unit

import com.hellofresh.oms.model.transferOrder.TransferOrderStatus
import com.hellofresh.oms.transferOrder.consumer.toTransferOrder
import com.hellofresh.oms.transferOrder.utils.Fixture
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Test
import java.util.UUID

class TransferOrderMapperTest {

    @Test
    fun `should map avro transfer order to entity correctly`() {
        // given
        val transferOrderId = UUID.randomUUID()
        val avroTransferOrder = Fixture.getAvroTransferOrder(
            id = transferOrderId,
            transferOrderNumber = "2552AET01638",
            sourceDcCode = "BL",
            destinationDcCode = "AE",
            status = "STATE_CANCELLED"
        )

        // when
        val result = avroTransferOrder.toTransferOrder()

        // then
        assertEquals(transferOrderId, result.id)
        assertEquals("2552AET01638", result.transferOrderNumber)
        assertEquals("BL", result.sourceDcCode)
        assertEquals("AE", result.destinationDcCode)
        assertEquals("<EMAIL>", result.creatorEmail)
        assertEquals("Replenishment", result.reasonText)
        assertEquals(TransferOrderStatus.STATE_CANCELLED, result.status)
        assertEquals(2025, result.yearWeek.getYear())
        assertEquals(52, result.yearWeek.getWeek())
        assertEquals("CA - Valley", result.sourceDcName)
        assertEquals("CA", result.marketCode)
        assertEquals(1, result.version)
    }

    @Test
    fun `should map total price correctly`() {
        // given
        val avroTransferOrder = Fixture.getAvroTransferOrder()

        // when
        val result = avroTransferOrder.toTransferOrder()

        // then
        assertNotNull(result.totalPrice)
        assertEquals("CAD", result.totalPrice.currencyCode)
        assertEquals(98L, result.totalPrice.units)
        assertEquals(10000000, result.totalPrice.nanos)
    }

    @Test
    fun `should map shipping information correctly`() {
        // given
        val avroTransferOrder = Fixture.getAvroTransferOrder()

        // when
        val result = avroTransferOrder.toTransferOrder()

        // then
        assertNotNull(result.shipping)
        assertEquals("Vendor delivered", result.shipping.method)

        val address = result.shipping.address
        assertEquals("CA", address.regionCode)
        assertEquals("T9E 1C6", address.postalCode)
        assertEquals("Alberta", address.administrativeArea)
        assertEquals("Nisku", address.locality)
        assertEquals(listOf("920", "36 Avenue"), address.addressLines)
        assertEquals("CA - EDM2", address.organization)
    }

    @Test
    fun `should map transfer order items correctly`() {
        // given
        val itemId1 = UUID.randomUUID()
        val itemId2 = UUID.randomUUID()
        val avroTransferOrder = Fixture.getAvroTransferOrderWithItems(
            itemIds = listOf(itemId1, itemId2)
        )

        // when
        val result = avroTransferOrder.toTransferOrder()

        // then
        assertEquals(2, result.items.size)

        val firstItem = result.items.first()
        assertEquals("PHF-10-88380-4", firstItem.cskuCode)
        assertEquals("X3B- Garlic, Unpeeled Clove", firstItem.cskuName)
        assertEquals("301330", firstItem.supplierCode)
        assertEquals(99, firstItem.orderSize)
        assertEquals("INVENTORY_INPUT_TYPE_MANUAL", firstItem.inventoryType)
        assertEquals("case", firstItem.packagingType)

        // Check price mapping
        assertEquals("CAD", firstItem.price.currencyCode)
        assertEquals(0L, firstItem.price.units)
        assertEquals(990000000, firstItem.price.nanos)

        // Check total price mapping
        assertEquals("CAD", firstItem.totalPrice.currencyCode)
        assertEquals(98L, firstItem.totalPrice.units)
        assertEquals(10000000, firstItem.totalPrice.nanos)

        // Check quantity mapping
        assertEquals("99", firstItem.quantity.value)

        // Check case packaging
        assertNotNull(firstItem.casePackaging)
        assertEquals("1", firstItem.casePackaging?.sizeValue)
        assertEquals("UOM_UNIT", firstItem.casePackaging?.unit)
    }

    @Test
    fun `should map optional fields correctly when present`() {
        // given
        val avroTransferOrder = Fixture.getAvroTransferOrderWithOptionalFields(
            comments = "Test comments",
            regionCode = "CA"
        )

        // when
        val result = avroTransferOrder.toTransferOrder()

        // then
        assertEquals("Test comments", result.comments)
        assertEquals("CA", result.regionCode)
    }

    @Test
    fun `should handle optional fields when not present`() {
        // given
        val avroTransferOrder = Fixture.getAvroTransferOrder()

        // when
        val result = avroTransferOrder.toTransferOrder()

        // then
        assertNull(result.comments)
        assertNull(result.deliveryChangeReason)
        assertNull(result.orderItemsChangeReason)
    }

    @Test
    fun `should map delivery change reason correctly`() {
        // given
        val avroTransferOrder = Fixture.getAvroTransferOrderWithDeliveryChangeReason(
            deliveryChangeReasonKey = "WEATHER",
            deliveryChangeReasonValue = "Bad weather conditions"
        )

        // when
        val result = avroTransferOrder.toTransferOrder()

        // then
        assertNotNull(result.deliveryChangeReason)
        assertEquals("WEATHER", result.deliveryChangeReason?.key)
        assertEquals("Bad weather conditions", result.deliveryChangeReason?.value)
    }

    @Test
    fun `should map order items change reason correctly`() {
        // given
        val avroTransferOrder = Fixture.getAvroTransferOrderWithOrderItemsChangeReason(
            orderItemsChangeReasonKey = "SHORTAGE",
            orderItemsChangeReasonValue = "Inventory shortage"
        )

        // when
        val result = avroTransferOrder.toTransferOrder()

        // then
        assertNotNull(result.orderItemsChangeReason)
        assertEquals("SHORTAGE", result.orderItemsChangeReason?.key)
        assertEquals("Inventory shortage", result.orderItemsChangeReason?.value)
    }
}
