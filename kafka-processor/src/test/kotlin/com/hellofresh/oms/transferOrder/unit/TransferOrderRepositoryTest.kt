package com.hellofresh.oms.transferOrder.unit

import com.hellofresh.oms.transferOrder.repository.TransferOrderRepository
import com.hellofresh.oms.transferOrder.utils.EntityFixture
import io.zonky.test.db.AutoConfigureEmbeddedDatabase
import io.zonky.test.db.AutoConfigureEmbeddedDatabase.DatabaseProvider.ZONKY
import io.zonky.test.db.AutoConfigureEmbeddedDatabase.DatabaseType.POSTGRES
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest

@DataJpaTest
@AutoConfigureEmbeddedDatabase(type = POSTGRES, provider = ZONKY)
class TransferOrderRepositoryTest(
    @Autowired private val transferOrderRepository: TransferOrderRepository
) {

    @Test
    fun `should save new transfer order entity`() {
        // given
        val transferOrderEntity = EntityFixture.getTransferOrderEntity()

        // when
        transferOrderRepository.save(transferOrderEntity)

        // then
        assertEquals(1, transferOrderRepository.findAll().size)
    }

    @Test
    fun `should save and update transfer order entity`() {
        // given
        val transferOrderEntity = EntityFixture.getTransferOrderEntity()
        val updatedComments = "Updated comments"

        // when
        transferOrderRepository.save(transferOrderEntity)
        val updatedEntity = transferOrderEntity.copy(comments = updatedComments)
        transferOrderRepository.save(updatedEntity)

        // then
        val savedEntities = transferOrderRepository.findAll()
        assertEquals(1, savedEntities.size)
        assertEquals(updatedComments, savedEntities[0].comments)
    }



    @Test
    fun `should save transfer order with JSONB shipping information`() {
        // given
        val transferOrderEntity = EntityFixture.getTransferOrderEntityWithShipping()

        // when
        transferOrderRepository.save(transferOrderEntity)

        // then
        val savedEntity = transferOrderRepository.findAll().first()
        assertNotNull(savedEntity.shipping)
        assertEquals("Vendor delivered", savedEntity.shipping.method)
        assertEquals("CA", savedEntity.shipping.address.regionCode)
        assertEquals("T9E 1C6", savedEntity.shipping.address.postalCode)
        assertEquals(listOf("920", "36 Avenue"), savedEntity.shipping.address.addressLines)
    }

    @Test
    fun `should save transfer order with items`() {
        // given
        val transferOrderEntity = EntityFixture.getTransferOrderEntityWithItems()

        // when
        transferOrderRepository.save(transferOrderEntity)

        // then
        val savedEntity = transferOrderRepository.findAll().first()
        assertEquals(2, savedEntity.items.size)

        val firstItem = savedEntity.items.first()
        assertEquals("PHF-10-88380-4", firstItem.cskuCode)
        assertEquals("case", firstItem.packagingType)
        assertNotNull(firstItem.casePackaging)
    }
}
