package com.hellofresh.oms.transferOrder.unit

import com.hellofresh.oms.transferOrder.repository.TransferOrderRepository
import com.hellofresh.oms.transferOrder.service.TransferOrderService
import com.hellofresh.oms.transferOrder.utils.EntityFixture
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Test
import java.util.UUID

class TransferOrderServiceTest {

    private val transferOrderRepository = mockk<TransferOrderRepository>()
    private val transferOrderService = TransferOrderService(transferOrderRepository)

    @Test
    fun `should upsert transfer order successfully`() {
        // given
        val transferOrderEntity = EntityFixture.getTransferOrderEntity()
        every { transferOrderRepository.save(transferOrderEntity) } returns transferOrderEntity

        // when
        val result = transferOrderService.upsertTransferOrder(transferOrderEntity)

        // then
        assertEquals(transferOrderEntity, result)
        verify { transferOrderRepository.save(transferOrderEntity) }
    }

    @Test
    fun `should find transfer order by transfer order number`() {
        // given
        val transferOrderNumber = "2552AET01638"
        val transferOrderEntity = EntityFixture.getTransferOrderEntity(
            transferOrderNumber = transferOrderNumber
        )
        every { transferOrderRepository.findByTransferOrderNumber(transferOrderNumber) } returns transferOrderEntity

        // when
        val result = transferOrderService.findByTransferOrderNumber(transferOrderNumber)

        // then
        assertEquals(transferOrderEntity, result)
        verify { transferOrderRepository.findByTransferOrderNumber(transferOrderNumber) }
    }

    @Test
    fun `should return null when transfer order not found by number`() {
        // given
        val transferOrderNumber = "NON_EXISTENT"
        every { transferOrderRepository.findByTransferOrderNumber(transferOrderNumber) } returns null

        // when
        val result = transferOrderService.findByTransferOrderNumber(transferOrderNumber)

        // then
        assertNull(result)
        verify { transferOrderRepository.findByTransferOrderNumber(transferOrderNumber) }
    }
}
