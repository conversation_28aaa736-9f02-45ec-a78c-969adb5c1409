@file:Suppress("LongParameterList", "ArgumentListWrapping", "ExpressionBodySyntax", "LongMethod")

package com.hellofresh.oms.transferOrder.utils

import com.google.protobuf.Timestamp
import com.google.type.Decimal
import com.google.type.Money
import com.google.type.PostalAddress
import com.hellofresh.proto.stream.transferOrder.v1.CasePackaging
import com.hellofresh.proto.stream.transferOrder.v1.DeliveryChangeReason
import com.hellofresh.proto.stream.transferOrder.v1.InventoryInputType
import com.hellofresh.proto.stream.transferOrder.v1.OrderItemsChangeReason
import com.hellofresh.proto.stream.transferOrder.v1.ProductionWeek
import com.hellofresh.proto.stream.transferOrder.v1.Shipping
import com.hellofresh.proto.stream.transferOrder.v1.State
import com.hellofresh.proto.stream.transferOrder.v1.TransferOrder
import com.hellofresh.proto.stream.transferOrder.v1.TransferOrderItem
import java.time.Instant
import java.util.UUID

@Suppress("LongMethod")
object Fixture {

    fun getAvroTransferOrder(
        id: UUID = UUID.randomUUID(),
        transferOrderNumber: String = "2552AET01638",
        sourceDcCode: String = "BL",
        destinationDcCode: String = "AE",
        status: String = "STATE_CANCELLED",
        creatorEmail: String = "<EMAIL>",
        reasonText: String = "Replenishment"
    ): TransferOrder {
        return TransferOrder.newBuilder()
            .setId(id.toString())
            .setSourceDcCode(sourceDcCode)
            .setDestinationDcCode(destinationDcCode)
            .setCreatorEmail(creatorEmail)
            .setReasonText(reasonText)
            .setStatus(State.valueOf(status))
            .setProductionWeek(
                ProductionWeek.newBuilder()
                    .setYear(2025)
                    .setWeek(52)
                    .build()
            )
            .setCreateTime(Timestamp.newBuilder().setSeconds(Instant.now().epochSecond).build())
            .setUpdateTime(Timestamp.newBuilder().setSeconds(Instant.now().epochSecond).build())
            .setPickupStartTime(
                Timestamp.newBuilder().setSeconds(Instant.parse("2025-12-22T10:00:00Z").epochSecond).build()
            )
            .setPickupEndTime(
                Timestamp.newBuilder().setSeconds(Instant.parse("2025-12-22T11:00:00Z").epochSecond).build()
            )
            .setDeliveryStartTime(
                Timestamp.newBuilder().setSeconds(Instant.parse("2025-12-23T12:00:00Z").epochSecond).build()
            )
            .setDeliveryEndTime(
                Timestamp.newBuilder().setSeconds(Instant.parse("2025-12-23T14:00:00Z").epochSecond).build()
            )
            .setSentTime(Timestamp.newBuilder().setSeconds(Instant.now().epochSecond).build())
            .setTransferOrderNumber(transferOrderNumber)
            .setSourceDcName("CA - Valley")
            .setShippingMethod("Vendor delivered")
            .setMarketCode("CA")
            .setVersion(1)
            .setTotalPrice(
                Money.newBuilder()
                    .setCurrencyCode("CAD")
                    .setUnits(98)
                    .setNanos(10000000)
                    .build()
            )
            .setShipping(
                Shipping.newBuilder()
                    .setAddress(
                        PostalAddress.newBuilder()
                            .setRegionCode("CA")
                            .setPostalCode("T9E 1C6")
                            .setAdministrativeArea("Alberta")
                            .setLocality("Nisku")
                            .addAddressLines("920")
                            .addAddressLines("36 Avenue")
                            .setOrganization("CA - EDM2")
                            .build()
                    )
                    .setMethod("Vendor delivered")
                    .build()
            )
            .addItems(getAvroTransferOrderItem())
            .build()
    }

    fun getAvroTransferOrderWithItems(
        id: UUID = UUID.randomUUID(),
        transferOrderNumber: String = "2552AET01639",
        itemIds: List<UUID>
    ): TransferOrder {
        val builder = getAvroTransferOrder(id = id, transferOrderNumber = transferOrderNumber)
            .toBuilder()
            .clearItems()

        itemIds.forEach { itemId ->
            builder.addItems(getAvroTransferOrderItem(id = itemId))
        }

        return builder.build()
    }

    fun getAvroTransferOrderWithOptionalFields(
        id: UUID = UUID.randomUUID(),
        comments: String? = null,
        regionCode: String? = null
    ): TransferOrder {
        val builder = getAvroTransferOrder(id = id).toBuilder()

        comments?.let { builder.setComments(it) }
        regionCode?.let { builder.setRegionCode(it) }

        return builder.build()
    }

    fun getAvroTransferOrderWithDeliveryChangeReason(
        id: UUID = UUID.randomUUID(),
        deliveryChangeReasonKey: String,
        deliveryChangeReasonValue: String
    ): TransferOrder {
        return getAvroTransferOrder(id = id)
            .toBuilder()
            .setDeliveryChangeReason(
                DeliveryChangeReason.newBuilder()
                    .setKey(deliveryChangeReasonKey)
                    .setValue(deliveryChangeReasonValue)
                    .build()
            )
            .build()
    }

    fun getAvroTransferOrderWithOrderItemsChangeReason(
        id: UUID = UUID.randomUUID(),
        orderItemsChangeReasonKey: String,
        orderItemsChangeReasonValue: String
    ): TransferOrder {
        return getAvroTransferOrder(id = id)
            .toBuilder()
            .setOrderItemsChangeReason(
                OrderItemsChangeReason.newBuilder()
                    .setKey(orderItemsChangeReasonKey)
                    .setValue(orderItemsChangeReasonValue)
                    .build()
            )
            .build()
    }

    fun getAvroTransferOrderItem(
        id: UUID = UUID.randomUUID(),
        cskuCode: String = "PHF-10-88380-4",
        cskuName: String = "X3B- Garlic, Unpeeled Clove",
        supplierId: UUID = UUID.fromString("64ba864f-d7b1-4216-8c98-90fc32f77335"),
        supplierCode: String = "301330",
        supplierSkuId: UUID = UUID.randomUUID(),
        skuId: UUID = UUID.fromString("77946983-7dc1-41fc-9f85-655131fce4a7"),
        orderSize: Int = 99
    ): TransferOrderItem {
        return TransferOrderItem.newBuilder()
            .setId(id.toString())
            .setCskuCode(cskuCode)
            .setCskuName(cskuName)
            .setSupplierId(supplierId.toString())
            .setSupplierCode(supplierCode)
            .setSupplierSkuId(supplierSkuId.toString())
            .setSkuId(skuId.toString())
            .setOrderSize(orderSize)
            .setInventoryType(InventoryInputType.INVENTORY_INPUT_TYPE_MANUAL)
            .setPrice(
                Money.newBuilder()
                    .setCurrencyCode("CAD")
                    .setNanos(990000000)
                    .build()
            )
            .setTotalPrice(
                Money.newBuilder()
                    .setCurrencyCode("CAD")
                    .setUnits(98)
                    .setNanos(10000000)
                    .build()
            )
            .setCasePackaging(
                CasePackaging.newBuilder()
                    .setSize(
                        Decimal.newBuilder()
                            .setValue("1")
                            .build()
                    )
                    .setUnit(CasePackaging.UOM.UOM_UNIT)
                    .build()
            )
            .setQuantity(
                Decimal.newBuilder()
                    .setValue("99")
                    .build()
            )
            .build()
    }

    fun getAvroTransferOrderItemWithOptionalFields(
        id: UUID = UUID.randomUUID(),
        originalPoNumber: String? = "PO123456",
        lotNumber: String? = "LOT789",
        licensePlateNumber: String? = "LP001"
    ): TransferOrderItem {
        val builder = getAvroTransferOrderItem(id = id).toBuilder()

        originalPoNumber?.let { builder.setOriginalPoNumber(it) }
        lotNumber?.let { builder.setLotNumber(it) }
        licensePlateNumber?.let { builder.setLicensePlateNumber(it) }

        return builder.build()
    }
}
