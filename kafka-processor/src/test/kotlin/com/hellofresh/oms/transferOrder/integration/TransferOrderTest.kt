package com.hellofresh.oms.transferOrder.integration

import com.hellofresh.oms.integrationTestUtils.AbstractIntegrationTest
import com.hellofresh.oms.integrationTestUtils.assertAwaiting
import com.hellofresh.oms.transferOrder.repository.TransferOrderRepository
import com.hellofresh.oms.transferOrder.utils.Fixture
import com.hellofresh.proto.stream.transferOrder.v1.TransferOrder as AvroTransferOrder
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.kafka.core.KafkaTemplate
import java.util.UUID

class TransferOrderTest : AbstractIntegrationTest() {

    @Autowired
    private lateinit var transferOrderRepository: TransferOrderRepository

    @Autowired
    private lateinit var kafkaTemplate: KafkaTemplate<String, AvroTransferOrder>

    @Value("\${topics.transfer-order}")
    private lateinit var topic: String

    @BeforeEach
    fun setUp() {
        transferOrderRepository.deleteAll()
    }

    @Test
    fun `should consume transfer order and save new entity - happy path`() {
        // given
        val transferOrderId = UUID.randomUUID()
        val avroTransferOrder = Fixture.getAvroTransferOrder(
            id = transferOrderId,
            transferOrderNumber = "2552AET01638",
            sourceDcCode = "BL",
            destinationDcCode = "AE",
            status = "STATE_CANCELLED"
        )

        // when
        publishMessage(avroTransferOrder, transferOrderId)

        // then
        assertAwaiting {
            val transferOrders = transferOrderRepository.findAll()
            assertEquals(1, transferOrders.size)
            
            val transferOrder = transferOrders.first()
            assertEquals(transferOrderId, transferOrder.id)
            assertEquals("2552AET01638", transferOrder.transferOrderNumber)
            assertEquals("BL", transferOrder.sourceDcCode)
            assertEquals("AE", transferOrder.destinationDcCode)
            assertEquals("STATE_CANCELLED", transferOrder.status.name)
            assertNotNull(transferOrder.shipping)
            assertEquals("Vendor delivered", transferOrder.shipping.method)
            assertEquals("CA", transferOrder.shipping.address.regionCode)
        }
    }

    @Test
    fun `should consume transfer order and update existing entity`() {
        // given
        val transferOrderId = UUID.randomUUID()
        val initialTransferOrder = Fixture.getAvroTransferOrder(
            id = transferOrderId,
            transferOrderNumber = "2552AET01638",
            status = "STATE_ORDERED"
        )
        
        val updatedTransferOrder = Fixture.getAvroTransferOrder(
            id = transferOrderId,
            transferOrderNumber = "2552AET01638",
            status = "STATE_DELIVERED"
        )

        // when
        publishMessage(initialTransferOrder, transferOrderId)
        
        assertAwaiting {
            assertEquals(1, transferOrderRepository.findAll().size)
        }
        
        publishMessage(updatedTransferOrder, transferOrderId)

        // then
        assertAwaiting {
            val transferOrders = transferOrderRepository.findAll()
            assertEquals(1, transferOrders.size)
            assertEquals("STATE_DELIVERED", transferOrders.first().status.name)
        }
    }

    @Test
    fun `should consume transfer order with multiple items`() {
        // given
        val transferOrderId = UUID.randomUUID()
        val item1Id = UUID.randomUUID()
        val item2Id = UUID.randomUUID()
        
        val avroTransferOrder = Fixture.getAvroTransferOrderWithItems(
            id = transferOrderId,
            transferOrderNumber = "2552AET01639",
            itemIds = listOf(item1Id, item2Id)
        )

        // when
        publishMessage(avroTransferOrder, transferOrderId)

        // then
        assertAwaiting {
            val transferOrders = transferOrderRepository.findAll()
            assertEquals(1, transferOrders.size)
            
            val transferOrder = transferOrders.first()
            assertEquals(2, transferOrder.items.size)
            
            val itemIds = transferOrder.items.map { it.id }
            assertEquals(setOf(item1Id, item2Id), itemIds.toSet())
        }
    }

    @Test
    fun `should handle transfer order with optional fields`() {
        // given
        val transferOrderId = UUID.randomUUID()
        val avroTransferOrder = Fixture.getAvroTransferOrderWithOptionalFields(
            id = transferOrderId,
            comments = "Test comments",
            regionCode = "CA"
        )

        // when
        publishMessage(avroTransferOrder, transferOrderId)

        // then
        assertAwaiting {
            val transferOrder = transferOrderRepository.findAll().first()
            assertEquals("Test comments", transferOrder.comments)
            assertEquals("CA", transferOrder.regionCode)
        }
    }

    @Test
    fun `should handle transfer order with delivery change reason`() {
        // given
        val transferOrderId = UUID.randomUUID()
        val avroTransferOrder = Fixture.getAvroTransferOrderWithDeliveryChangeReason(
            id = transferOrderId,
            deliveryChangeReasonKey = "WEATHER",
            deliveryChangeReasonValue = "Bad weather conditions"
        )

        // when
        publishMessage(avroTransferOrder, transferOrderId)

        // then
        assertAwaiting {
            val transferOrder = transferOrderRepository.findAll().first()
            assertNotNull(transferOrder.deliveryChangeReason)
            assertEquals("WEATHER", transferOrder.deliveryChangeReason?.key)
            assertEquals("Bad weather conditions", transferOrder.deliveryChangeReason?.value)
        }
    }

    private fun publishMessage(avroTransferOrder: AvroTransferOrder, key: UUID) {
        kafkaTemplate.send(topic, key.toString(), avroTransferOrder).get()
    }
}
