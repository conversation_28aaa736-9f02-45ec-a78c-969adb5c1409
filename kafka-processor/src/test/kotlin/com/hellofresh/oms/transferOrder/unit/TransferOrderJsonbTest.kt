package com.hellofresh.oms.transferOrder.unit

import com.fasterxml.jackson.databind.ObjectMapper
import com.hellofresh.oms.model.transferOrder.AddressInfo
import com.hellofresh.oms.model.transferOrder.ShippingInfo
import com.hellofresh.oms.transferOrder.repository.TransferOrderRepository
import com.hellofresh.oms.transferOrder.utils.EntityFixture
import io.zonky.test.db.AutoConfigureEmbeddedDatabase
import io.zonky.test.db.AutoConfigureEmbeddedDatabase.DatabaseProvider.ZONKY
import io.zonky.test.db.AutoConfigureEmbeddedDatabase.DatabaseType.POSTGRES
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest
import org.springframework.boot.test.autoconfigure.orm.jpa.TestEntityManager
import org.springframework.test.context.jdbc.Sql

@DataJpaTest
@AutoConfigureEmbeddedDatabase(type = POSTGRES, provider = ZONKY)
class TransferOrderJsonbTest(
    @Autowired private val transferOrderRepository: TransferOrderRepository,
    @Autowired private val entityManager: TestEntityManager
) {

    private val objectMapper = ObjectMapper()

    @Test
    fun `should serialize and deserialize shipping info as JSONB`() {
        // given
        val shippingInfo = ShippingInfo(
            address = AddressInfo(
                regionCode = "US",
                postalCode = "85043",
                administrativeArea = "AZ",
                locality = "Phoenix",
                addressLines = listOf("1850", "S 71st Avenue"),
                organization = "United States - AZ",
                recipients = listOf("John Doe"),
                revision = 1
            ),
            method = "Freight on board",
            notes = "Handle with care"
        )
        
        val transferOrder = EntityFixture.getTransferOrderEntity().copy(
            shipping = shippingInfo
        )

        // when
        val savedEntity = transferOrderRepository.save(transferOrder)
        entityManager.flush()
        entityManager.clear()

        // then
        val retrievedEntity = transferOrderRepository.findById(savedEntity.id).orElse(null)
        assertNotNull(retrievedEntity)
        
        val retrievedShipping = retrievedEntity.shipping
        assertEquals("Freight on board", retrievedShipping.method)
        assertEquals("Handle with care", retrievedShipping.notes)
        
        val retrievedAddress = retrievedShipping.address
        assertEquals("US", retrievedAddress.regionCode)
        assertEquals("85043", retrievedAddress.postalCode)
        assertEquals("AZ", retrievedAddress.administrativeArea)
        assertEquals("Phoenix", retrievedAddress.locality)
        assertEquals(listOf("1850", "S 71st Avenue"), retrievedAddress.addressLines)
        assertEquals("United States - AZ", retrievedAddress.organization)
        assertEquals(listOf("John Doe"), retrievedAddress.recipients)
        assertEquals(1, retrievedAddress.revision)
    }

    @Test
    @Sql("classpath:sql/transfer_order_jsonb_test.sql")
    fun `should query JSONB fields using native queries`() {
        // when - query by shipping method using JSONB operator
        val result = entityManager.entityManager.createNativeQuery(
            "SELECT COUNT(*) FROM transfer_order WHERE shipping->>'method' = :method"
        ).setParameter("method", "Freight on board").singleResult

        // then
        assertEquals(1L, result)
    }

    @Test
    @Sql("classpath:sql/transfer_order_jsonb_test.sql")
    fun `should query nested JSONB fields`() {
        // when - query by address region code using nested JSONB operator
        val result = entityManager.entityManager.createNativeQuery(
            "SELECT COUNT(*) FROM transfer_order WHERE shipping->'address'->>'regionCode' = :regionCode"
        ).setParameter("regionCode", "US").singleResult

        // then
        assertEquals(1L, result)
    }

    @Test
    fun `should handle null optional fields in JSONB`() {
        // given
        val shippingInfo = ShippingInfo(
            address = AddressInfo(
                regionCode = "CA",
                postalCode = "T9E 1C6",
                administrativeArea = "Alberta",
                locality = "Nisku",
                addressLines = listOf("920", "36 Avenue"),
                organization = "CA - EDM2",
                recipients = null, // null optional field
                revision = null    // null optional field
            ),
            method = "Vendor delivered",
            notes = null // null optional field
        )
        
        val transferOrder = EntityFixture.getTransferOrderEntity().copy(
            shipping = shippingInfo
        )

        // when
        val savedEntity = transferOrderRepository.save(transferOrder)
        entityManager.flush()
        entityManager.clear()

        // then
        val retrievedEntity = transferOrderRepository.findById(savedEntity.id).orElse(null)
        assertNotNull(retrievedEntity)
        
        val retrievedShipping = retrievedEntity.shipping
        assertEquals("Vendor delivered", retrievedShipping.method)
        assertEquals(null, retrievedShipping.notes)
        
        val retrievedAddress = retrievedShipping.address
        assertEquals(null, retrievedAddress.recipients)
        assertEquals(null, retrievedAddress.revision)
    }

    @Test
    fun `should preserve JSON structure in database`() {
        // given
        val transferOrder = EntityFixture.getTransferOrderEntityWithShipping()

        // when
        transferOrderRepository.save(transferOrder)
        entityManager.flush()

        // then - verify the JSON structure is preserved in the database
        val jsonResult = entityManager.entityManager.createNativeQuery(
            "SELECT shipping FROM transfer_order WHERE id = :id"
        ).setParameter("id", transferOrder.id).singleResult as String

        val jsonNode = objectMapper.readTree(jsonResult)
        assertEquals("Vendor delivered", jsonNode.get("method").asText())
        assertEquals("Handle with care", jsonNode.get("notes").asText())
        assertEquals("CA", jsonNode.get("address").get("regionCode").asText())
        assertEquals("T9E 1C6", jsonNode.get("address").get("postalCode").asText())
    }
}
