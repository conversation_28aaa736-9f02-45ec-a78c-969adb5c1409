@file:Suppress("ImportOrdering")

package com.hellofresh.oms.transferOrder.unit

import com.hellofresh.oms.transferOrder.consumer.TransferOrderConsumer
import com.hellofresh.oms.transferOrder.service.TransferOrderService
import com.hellofresh.oms.transferOrder.utils.EntityFixture
import com.hellofresh.oms.transferOrder.utils.Fixture
import com.hellofresh.proto.stream.transferOrder.v1.TransferOrder as AvroTransferOrder
import org.junit.jupiter.api.Assertions.assertThrows
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito.verify
import org.mockito.Mockito.`when`
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.any
import org.springframework.messaging.Message
import org.springframework.messaging.support.MessageBuilder
import java.util.UUID

@ExtendWith(MockitoExtension::class)
class TransferOrderConsumerTest {

    @Mock
    private lateinit var transferOrderService: TransferOrderService

    @InjectMocks
    private lateinit var transferOrderConsumer: TransferOrderConsumer

    @Test
    fun `should process transfer order message successfully`() {
        // given
        val transferOrderId = UUID.randomUUID()
        val avroTransferOrder = Fixture.getAvroTransferOrder(id = transferOrderId)
        val message: Message<AvroTransferOrder> = MessageBuilder.withPayload(avroTransferOrder).build()
        val transferOrderEntity = EntityFixture.getTransferOrderEntity(id = transferOrderId)

        `when`(transferOrderService.upsertTransferOrder(any())).thenReturn(transferOrderEntity)

        // when
        transferOrderConsumer.processTransferOrder().accept(message)

        // then
        verify(transferOrderService).upsertTransferOrder(any())
    }

    @Test
    fun `should handle service exception and rethrow`() {
        // given
        val avroTransferOrder = Fixture.getAvroTransferOrder()
        val message: Message<AvroTransferOrder> = MessageBuilder.withPayload(avroTransferOrder).build()
        val exception = RuntimeException("Database error")

        `when`(transferOrderService.upsertTransferOrder(any())).thenThrow(exception)

        // when & then
        assertThrows(RuntimeException::class.java) {
            transferOrderConsumer.processTransferOrder().accept(message)
        }

        verify(transferOrderService).upsertTransferOrder(any())
    }

    @Test
    fun `should process transfer order with multiple items`() {
        // given
        val transferOrderId = UUID.randomUUID()
        val itemIds = listOf(UUID.randomUUID(), UUID.randomUUID())
        val avroTransferOrder = Fixture.getAvroTransferOrderWithItems(
            id = transferOrderId,
            itemIds = itemIds
        )
        val message: Message<AvroTransferOrder> = MessageBuilder.withPayload(avroTransferOrder).build()
        val transferOrderEntity = EntityFixture.getTransferOrderEntityWithItems(id = transferOrderId)

        `when`(transferOrderService.upsertTransferOrder(any())).thenReturn(transferOrderEntity)

        // when
        transferOrderConsumer.processTransferOrder().accept(message)

        // then
        verify(transferOrderService).upsertTransferOrder(any())
    }

    @Test
    fun `should process transfer order with optional fields`() {
        // given
        val avroTransferOrder = Fixture.getAvroTransferOrderWithOptionalFields(
            comments = "Test comments",
            regionCode = "CA"
        )
        val message: Message<AvroTransferOrder> = MessageBuilder.withPayload(avroTransferOrder).build()
        val transferOrderEntity = EntityFixture.getTransferOrderEntity()

        `when`(transferOrderService.upsertTransferOrder(any())).thenReturn(transferOrderEntity)

        // when
        transferOrderConsumer.processTransferOrder().accept(message)

        // then
        verify(transferOrderService).upsertTransferOrder(any())
    }

    @Test
    fun `should process transfer order with change reasons`() {
        // given
        val avroTransferOrder = Fixture.getAvroTransferOrderWithDeliveryChangeReason(
            deliveryChangeReasonKey = "WEATHER",
            deliveryChangeReasonValue = "Bad weather conditions"
        )
        val message: Message<AvroTransferOrder> = MessageBuilder.withPayload(avroTransferOrder).build()
        val transferOrderEntity = EntityFixture.getTransferOrderEntityWithChangeReasons()

        `when`(transferOrderService.upsertTransferOrder(any())).thenReturn(transferOrderEntity)

        // when
        transferOrderConsumer.processTransferOrder().accept(message)

        // then
        verify(transferOrderService).upsertTransferOrder(any())
    }
}
