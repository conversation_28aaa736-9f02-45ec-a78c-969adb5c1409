package com.hellofresh.oms.transferOrder.integration

import com.hellofresh.oms.integrationTestUtils.AbstractIntegrationTest
import com.hellofresh.oms.integrationTestUtils.assertAwaiting
import com.hellofresh.oms.model.transferOrder.TransferOrderStatus
import com.hellofresh.oms.transferOrder.repository.TransferOrderRepository
import com.hellofresh.oms.transferOrder.utils.Fixture
import com.hellofresh.proto.stream.transferOrder.v1.State
import com.hellofresh.proto.stream.transferOrder.v1.TransferOrder as AvroTransferOrder
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.kafka.core.KafkaTemplate
import java.util.UUID

class TransferOrderEndToEndTest : AbstractIntegrationTest() {

    @Autowired
    private lateinit var transferOrderRepository: TransferOrderRepository

    @Autowired
    private lateinit var kafkaTemplate: KafkaTemplate<String, AvroTransferOrder>

    @Value("\${topics.transfer-order}")
    private lateinit var topic: String

    @BeforeEach
    fun setUp() {
        transferOrderRepository.deleteAll()
    }

    @Test
    fun `should handle complete transfer order lifecycle`() {
        val transferOrderId = UUID.randomUUID()
        
        // Step 1: Create transfer order
        val createdTransferOrder = Fixture.getAvroTransferOrder(
            id = transferOrderId,
            transferOrderNumber = "2552AET01638",
            status = "STATE_OPEN"
        )
        
        publishMessage(createdTransferOrder, transferOrderId)
        
        assertAwaiting {
            val transferOrders = transferOrderRepository.findAll()
            assertEquals(1, transferOrders.size)
            assertEquals(TransferOrderStatus.STATE_OPEN, transferOrders.first().status)
        }
        
        // Step 2: Order transfer order
        val orderedTransferOrder = createdTransferOrder.toBuilder()
            .setStatus(State.STATE_ORDERED)
            .build()
            
        publishMessage(orderedTransferOrder, transferOrderId)
        
        assertAwaiting {
            val transferOrder = transferOrderRepository.findAll().first()
            assertEquals(TransferOrderStatus.STATE_ORDERED, transferOrder.status)
        }
        
        // Step 3: Accept transfer order
        val acceptedTransferOrder = orderedTransferOrder.toBuilder()
            .setStatus(State.STATE_ACCEPTED)
            .build()
            
        publishMessage(acceptedTransferOrder, transferOrderId)
        
        assertAwaiting {
            val transferOrder = transferOrderRepository.findAll().first()
            assertEquals(TransferOrderStatus.STATE_ACCEPTED, transferOrder.status)
        }
        
        // Step 4: Ship transfer order
        val shippedTransferOrder = acceptedTransferOrder.toBuilder()
            .setStatus(State.STATE_IN_TRANSIT)
            .build()
            
        publishMessage(shippedTransferOrder, transferOrderId)
        
        assertAwaiting {
            val transferOrder = transferOrderRepository.findAll().first()
            assertEquals(TransferOrderStatus.STATE_IN_TRANSIT, transferOrder.status)
        }
        
        // Step 5: Deliver transfer order
        val deliveredTransferOrder = shippedTransferOrder.toBuilder()
            .setStatus(State.STATE_DELIVERED)
            .build()
            
        publishMessage(deliveredTransferOrder, transferOrderId)
        
        assertAwaiting {
            val transferOrder = transferOrderRepository.findAll().first()
            assertEquals(TransferOrderStatus.STATE_DELIVERED, transferOrder.status)
            assertEquals(1, transferOrderRepository.count()) // Still only one record
        }
    }

    @Test
    fun `should handle transfer order with complex shipping information`() {
        // given
        val transferOrderId = UUID.randomUUID()
        val avroTransferOrder = Fixture.getAvroTransferOrder(
            id = transferOrderId,
            transferOrderNumber = "2552AET01639"
        )

        // when
        publishMessage(avroTransferOrder, transferOrderId)

        // then
        assertAwaiting {
            val transferOrder = transferOrderRepository.findAll().first()
            
            // Verify shipping information is properly stored as JSONB
            assertNotNull(transferOrder.shipping)
            assertEquals("Vendor delivered", transferOrder.shipping.method)
            
            val address = transferOrder.shipping.address
            assertEquals("CA", address.regionCode)
            assertEquals("T9E 1C6", address.postalCode)
            assertEquals("Alberta", address.administrativeArea)
            assertEquals("Nisku", address.locality)
            assertEquals(listOf("920", "36 Avenue"), address.addressLines)
            assertEquals("CA - EDM2", address.organization)
        }
    }

    @Test
    fun `should handle transfer order with all optional fields populated`() {
        // given
        val transferOrderId = UUID.randomUUID()
        val avroTransferOrder = Fixture.getAvroTransferOrderWithOptionalFields(
            id = transferOrderId,
            comments = "Urgent delivery required",
            regionCode = "CA"
        ).toBuilder()
            .setDeliveryChangeReason(
                com.hellofresh.proto.stream.transferOrder.v1.DeliveryChangeReason.newBuilder()
                    .setKey("WEATHER")
                    .setValue("Storm expected")
                    .build()
            )
            .setOrderItemsChangeReason(
                com.hellofresh.proto.stream.transferOrder.v1.OrderItemsChangeReason.newBuilder()
                    .setKey("SHORTAGE")
                    .setValue("Supplier shortage")
                    .build()
            )
            .build()

        // when
        publishMessage(avroTransferOrder, transferOrderId)

        // then
        assertAwaiting {
            val transferOrder = transferOrderRepository.findAll().first()
            
            assertEquals("Urgent delivery required", transferOrder.comments)
            assertEquals("CA", transferOrder.regionCode)
            
            assertNotNull(transferOrder.deliveryChangeReason)
            assertEquals("WEATHER", transferOrder.deliveryChangeReason?.key)
            assertEquals("Storm expected", transferOrder.deliveryChangeReason?.value)
            
            assertNotNull(transferOrder.orderItemsChangeReason)
            assertEquals("SHORTAGE", transferOrder.orderItemsChangeReason?.key)
            assertEquals("Supplier shortage", transferOrder.orderItemsChangeReason?.value)
        }
    }

    @Test
    fun `should handle transfer order with multiple items and different packaging types`() {
        // given
        val transferOrderId = UUID.randomUUID()
        val item1Id = UUID.randomUUID()
        val item2Id = UUID.randomUUID()
        
        val avroTransferOrder = Fixture.getAvroTransferOrderWithItems(
            id = transferOrderId,
            transferOrderNumber = "2552AET01640",
            itemIds = listOf(item1Id, item2Id)
        )

        // when
        publishMessage(avroTransferOrder, transferOrderId)

        // then
        assertAwaiting {
            val transferOrder = transferOrderRepository.findAll().first()
            assertEquals(2, transferOrder.items.size)
            
            // Verify all items have proper packaging information
            transferOrder.items.forEach { item ->
                assertEquals("case", item.packagingType)
                assertNotNull(item.casePackaging)
                assertEquals("1", item.casePackaging?.sizeValue)
                assertEquals("UOM_UNIT", item.casePackaging?.unit)
                
                // Verify price information
                assertEquals("CAD", item.price.currencyCode)
                assertEquals("CAD", item.totalPrice.currencyCode)
                
                // Verify quantity
                assertEquals("99", item.quantity.value)
            }
        }
    }

    @Test
    fun `should handle concurrent updates to same transfer order`() {
        // given
        val transferOrderId = UUID.randomUUID()
        val transferOrder1 = Fixture.getAvroTransferOrder(
            id = transferOrderId,
            status = "STATE_ORDERED"
        )
        val transferOrder2 = Fixture.getAvroTransferOrder(
            id = transferOrderId,
            status = "STATE_ACCEPTED"
        )

        // when - publish messages concurrently
        publishMessage(transferOrder1, transferOrderId)
        publishMessage(transferOrder2, transferOrderId)

        // then - should have only one record with the latest status
        assertAwaiting {
            val transferOrders = transferOrderRepository.findAll()
            assertEquals(1, transferOrders.size)
            assertTrue(
                transferOrders.first().status == TransferOrderStatus.STATE_ORDERED ||
                transferOrders.first().status == TransferOrderStatus.STATE_ACCEPTED
            )
        }
    }

    private fun publishMessage(avroTransferOrder: AvroTransferOrder, key: UUID) {
        kafkaTemplate.send(topic, key.toString(), avroTransferOrder).get()
    }
}
