---
services:
  broker:
    image: bitnami/kafka:2.8.0
    hostname: broker
    container_name: broker
    depends_on:
      - zookeeper
    ports:
      - '29092:29092'
      - '9092:9092'
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_INTER_BROKER_LISTENER_NAME: LISTENER_DOCKER_INTERNAL
      ALLOW_PLAINTEXT_LISTENER: 'yes'
      KAFKA_CFG_ZOOKEEPER_CONNECT: 'zookeeper:2181'
      KAFKA_CFG_LISTENER_SECURITY_PROTOCOL_MAP: LISTENER_DOCKER_INTERNAL:PLAINTEXT,PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT,EXTERNAL:PLAINTEXT
      KAFKA_CFG_ADVERTISED_LISTENERS: LISTENER_DOCKER_INTERNAL://broker:19092,PLAINTEXT://localhost:9092,EXTERNAL://localhost:29092
      KAFKA_CFG_LISTENERS: LISTENER_DOCKER_INTERNAL://:19092,P<PERSON>INTEXT://:9092,EXTERNAL://:29092
      KAFKA_HEAP_OPTS: "-Xmx2048m -Xms1024m -XX:-TieredCompilation -XX:+UseStringDeduplication -noverify"
    volumes:
      - ./docker/dev:/usr/src
    healthcheck:
      test:
        - CMD-SHELL
        - /usr/src/create-topics.sh "public.planning.facility.v1"
      interval: 30s
      timeout: 120s
      retries: 5
      start_period: 15s

  zookeeper:
    image: bitnami/zookeeper:3.7.0
    hostname: zookeeper
    container_name: zookeeper
    ports:
      - '2181:2181'
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
      ALLOW_ANONYMOUS_LOGIN: 'yes'

  akhq:
    image: tchiotludo/akhq
    container_name: akhq
    environment:
      AKHQ_CONFIGURATION: |
        akhq:
          connections:
            docker-kafka-server:
              properties:
                bootstrap.servers: "broker:19092"

    ports:
      - 19000:8080
    depends_on:
      - broker
    healthcheck:
      test: "${DOCKER_HEALTHCHECK_TEST:-curl localhost:28081/health}"
      interval: "60s"
      timeout: "3s"
      start_period: "5s"
      retries: 3

  postgres:
    image: postgres:14-alpine
    container_name: postgres
    ports:
      - "${DB_PORT:-5432}:5432"
    healthcheck:
      test: [ "CMD-SHELL", "pg_isready -U ${DB_USERNAME} -d ${DB_NAME}" ]
      interval: 30s
      timeout: 30s
      retries: 3
    environment:
      LC_ALL: C.UTF-8
      POSTGRES_USER: "root"
      POSTGRES_PASSWORD: "123456"
      POSTGRES_DB: "postgres"

  zipkin:
    image: openzipkin/zipkin
    container_name: zipkin
    ports:
      - "9411:9411"
