name: 'Delete Kafka Consumer Group'
description: 'Deletes a Kafka Consumer Group on a given environment and topic'

inputs:
  env:
    description: 'Target environment'
    required: true
    default: 'staging'
  consumer-group:
    description: 'Consumer group'
    required: true
  topic:
    description: 'Topic'
    required: true

runs:
  using: "composite"
  steps:
    - name: 🚪 Checkout source code
      uses: actions/checkout@v4

    - name: 🤫 Import Secrets
      id: vault-secrets
      uses: ./.github/actions/vault
      with:
        secrets: |
          ${{ inputs.env }}/key-value/data/secrets HF_AIVEN_USERNAME;
          ${{ inputs.env }}/key-value/data/secrets HF_AIVEN_PASSWORD;
          ${{ inputs.env }}/key-value/data/secrets HF_KAFKA_TRUSTSTORE_PASSWORD;
          ${{ inputs.env }}/key-value/data/secrets KAFKA_BROKER;
          common/key-value/data/secrets SLACK_URL;

    - name: Set secrets in config
      shell: bash
      run: |
        cp ./kafka-processor/src/main/resources/kafka/${{ inputs.env }}-truststore.jks ./.github/config
        echo "ssl.truststore.location=/oms/${{ inputs.env }}-truststore.jks" >> ./.github/config/kafka-cli.config
        echo "ssl.truststore.password=${{ env.HF_KAFKA_TRUSTSTORE_PASSWORD }}" >> ./.github/config/kafka-cli.config
        echo "sasl.jaas.config=org.apache.kafka.common.security.plain.PlainLoginModule required username='${{ env.HF_AIVEN_USERNAME }}' password='${{ env.HF_AIVEN_PASSWORD }}';" >> ./.github/config/kafka-cli.config

    - name: Docker run kafka-consumer-groups delete-offsets
      shell: bash
      run: |
        docker run -v "./.github/config:/oms" confluentinc/cp-kafka \
          kafka-consumer-groups --bootstrap-server ${{ env.KAFKA_BROKER }} \
                                --command-config /oms/kafka-cli.config \
                                --group ${{ inputs.consumer-group }} \
                                --topic ${{ inputs.topic }} \
                                --delete-offsets &> ./out.txt; ! grep -e Exception -e Error ./out.txt
        exit $?

    - name: 📣 Slack notification about job failure
      if: ${{ failure() }}
      uses: hellofresh/jetstream-ci-scripts/actions/slack-notification@master
      with:
        slack-url: ${{ env.SLACK_URL }}
        channel: "#squad-purchase-order-lifecycle-alerts-${{ inputs.env }}"
        icon-emoji: ":kafka:"
        color: "danger"
        pretext: "Failed to delete consumer group"
        text: "Failed to delete consumer group ${{ inputs.consumer-group }} for topic ${{ inputs.topic }}"
        buttons: |
          https://github.com/hellofresh/order-management-service/actions/runs/${{ github.run_id }}|Logs

    - name: 📣 Slack notification about job success
      if: ${{ success() }}
      uses: hellofresh/jetstream-ci-scripts/actions/slack-notification@master
      with:
        slack-url: ${{ env.SLACK_URL }}
        channel: "#squad-purchase-order-lifecycle-deployments"
        icon-emoji: ":kafka:"
        color: "good"
        pretext: "Successfully deleted consumer group on ${{ inputs.env }}"
        text: "Successfully deleted consumer group ${{ inputs.consumer-group }} for topic ${{ inputs.topic }}"
