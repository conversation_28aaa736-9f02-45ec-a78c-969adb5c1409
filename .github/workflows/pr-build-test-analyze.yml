name: PR - B<PERSON>, test, analyze
concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref }}
  cancel-in-progress: true

permissions:
  id-token: write
  contents: read

on: [pull_request]

jobs:
  prepare_modified_files:
    name: 🗃 Modules modified
    runs-on: [ self-hosted, default ]
    steps:
      - name: 🚪 Checkout source code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: 📖 Read config file
        id: config
        run: echo "config=$(jq -c . ./.github/config/modules.json)" >> $GITHUB_OUTPUT

      - name: 👀 Get modified modules
        id: check_modified
        uses: ./.github/actions/check_modified
        with:
          config: ${{ steps.config.outputs.config }}
          terraform_path: 'terraform/**'
          db_migration_path: 'db-migration/**'
          common_paths: ".github/**, model/**, .java-version, gradle/**, build.gradle.kts, gradle.properties, settings.gradle.kts, db-migration/**"

    outputs:
      modules: ${{ steps.check_modified.outputs.modified_modules }}
      is_db_migration_changed: ${{ steps.check_modified.outputs.db_migration == 'true' }}

  build-test-analyze:
    runs-on: [self-hosted, heavy]
    timeout-minutes: 15
    needs: [ prepare_modified_files ]
    steps:
      - name: 🚪 Checkout source code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: 🤫 Import Secrets
        id: vault-secrets
        uses: hellofresh/jetstream-ci-scripts/actions/vault@master
        with:
          shared-secrets: |
            common/data/defaults artifactory_username | ARTIFACTORY_USERNAME;
            common/data/defaults artifactory_password | ARTIFACTORY_PASSWORD;
            common/data/defaults SONAR_TOKEN | SONAR_TOKEN;

      - name: 📋 Check Gradle
        shell: bash
        run: |
          gradle check jacocoAggregatedReport sonar \
            "-Dsonar.token=${{ steps.vault-secrets.outputs.SONAR_TOKEN }}" \
            "-Dsonar.pullrequest.branch=${{ github.event.pull_request.head.ref }}" \
            "-Dsonar.pullrequest.key=${{ github.event.pull_request.number }}" \
            "-Dsonar.pullrequest.base=${{ github.event.pull_request.base.ref }}"
        env:
          ARTIFACTORY_USERNAME: ${{ env.ARTIFACTORY_USERNAME }}
          ARTIFACTORY_PASSWORD: ${{ env.ARTIFACTORY_PASSWORD }}

  test_integration:
    runs-on: [self-hosted, default]
    timeout-minutes: 15
    needs: [ prepare_modified_files ]
    if: ${{ needs.prepare_modified_files.outputs.modules != '[]' && needs.prepare_modified_files.outputs.modules != '' }}
    strategy:
      matrix:
        modules: ${{ fromJSON(needs.prepare_modified_files.outputs.modules) }}
    steps:
      - name: 🚪 Checkout source code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: 🤫 Import Secrets
        id: vault-secrets
        uses: hellofresh/jetstream-ci-scripts/actions/vault@master
        with:
          shared-secrets: |
            common/data/defaults artifactory_username | ARTIFACTORY_USERNAME;
            common/data/defaults artifactory_password | ARTIFACTORY_PASSWORD;
            common/data/defaults SONAR_TOKEN | SONAR_TOKEN;

      - name: Gradle Test-Integration - ${{ matrix.modules.module }}
        shell: bash
        run: |
          gradle ${{ matrix.modules.module }}:integrationTest
        env:
          ARTIFACTORY_USERNAME: ${{ env.ARTIFACTORY_USERNAME }}
          ARTIFACTORY_PASSWORD: ${{ env.ARTIFACTORY_PASSWORD }}

  end-to-end-tests:
    runs-on: [self-hosted, heavy]
    if: ${{ !contains(github.event.pull_request.labels.*.name, 'skip-e2e') }}
    steps:
      - name: 🚪 Checkout source code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: 🤫 Import Shared Secrets
        id: vault-secrets
        uses: hellofresh/jetstream-ci-scripts/actions/vault@master
        with:
          shared-secrets: |
            common/data/defaults artifactory_username | ARTIFACTORY_USERNAME;
            common/data/defaults artifactory_password | ARTIFACTORY_PASSWORD;
          namespace: services/order-management-service
          secrets: |
            common/key-value/data/ci CYPRESS_RECORD_KEY | CYPRESS_RECORD_KEY;
            common/key-value/data/ci NEXT_PUBLIC_STATSIG_API_KEY | NEXT_PUBLIC_STATSIG_API_KEY;

      - name: 🧪 run end-to-end tests
        uses: ./.github/actions/end-to-end
        with:
          max_attempts: 3
          retry_on: error
          orderManagementBackendTag: "build"
